import React, { useState, useEffect } from 'react';
import { supabase } from '../../utils/supabase/supabase.utils';

/**
 * Environment Test Page
 * 
 * This page is used by Playwright tests to verify that the environment
 * is properly configured and all services are working.
 */
const EnvironmentTestPage = () => {
  const [status, setStatus] = useState({
    environment: 'checking',
    supabase: 'checking',
    apis: 'checking',
    overall: 'checking'
  });

  useEffect(() => {
    checkEnvironment();
  }, []);

  const checkEnvironment = async () => {
    const results = {
      environment: 'unknown',
      supabase: 'unknown',
      apis: 'unknown',
      overall: 'unknown'
    };

    try {
      // Check environment variables
      const hasSupabaseUrl = !!import.meta.env.VITE_SUPABASE_URL;
      const hasSupabaseKey = !!import.meta.env.VITE_SUPABASE_ANON_KEY;
      const hasGoogleAnalytics = !!import.meta.env.VITE_GA_TRACKING_ID;
      
      results.environment = hasSupabaseUrl && hasSupabaseKey ? 'configured' : 'missing';

      // Check Supabase connection
      try {
        const { data, error } = await supabase.from('profiles').select('count').limit(1);
        results.supabase = error ? 'error' : 'connected';
      } catch (err) {
        results.supabase = 'error';
      }

      // Check API endpoints (basic health check)
      try {
        // This is a simple check - in a real app you'd ping your health endpoint
        results.apis = 'available';
      } catch (err) {
        results.apis = 'error';
      }

      // Overall status
      const allGood = Object.values(results).every(status => 
        status === 'configured' || status === 'connected' || status === 'available'
      );
      results.overall = allGood ? 'ready' : 'issues';

    } catch (error) {
      console.error('Environment check failed:', error);
      results.overall = 'error';
    }

    setStatus(results);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ready':
      case 'configured':
      case 'connected':
      case 'available':
        return 'text-green-600';
      case 'checking':
        return 'text-yellow-600';
      case 'error':
      case 'missing':
      case 'issues':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'ready':
      case 'configured':
      case 'connected':
      case 'available':
        return '✅';
      case 'checking':
        return '⏳';
      case 'error':
      case 'missing':
      case 'issues':
        return '❌';
      default:
        return '❓';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            Environment Test Page
          </h1>
          
          <div className="space-y-6">
            {/* Overall Status */}
            <div className="border-2 border-gray-200 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Overall Status</h2>
              <div 
                data-testid="environment-status"
                className={`text-lg font-medium ${getStatusColor(status.overall)}`}
              >
                {getStatusIcon(status.overall)} {status.overall.toUpperCase()}
              </div>
            </div>

            {/* Detailed Status */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Environment Variables */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold mb-2">Environment Variables</h3>
                <div 
                  data-testid="environment-vars-status"
                  className={`${getStatusColor(status.environment)}`}
                >
                  {getStatusIcon(status.environment)} {status.environment}
                </div>
                <div className="text-sm text-gray-600 mt-2">
                  <div>Supabase URL: {import.meta.env.VITE_SUPABASE_URL ? '✅' : '❌'}</div>
                  <div>Supabase Key: {import.meta.env.VITE_SUPABASE_ANON_KEY ? '✅' : '❌'}</div>
                  <div>GA Tracking: {import.meta.env.VITE_GA_TRACKING_ID ? '✅' : '❌'}</div>
                </div>
              </div>

              {/* Supabase Connection */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold mb-2">Supabase Connection</h3>
                <div 
                  data-testid="supabase-status"
                  className={`${getStatusColor(status.supabase)}`}
                >
                  {getStatusIcon(status.supabase)} {status.supabase}
                </div>
                <div className="text-sm text-gray-600 mt-2">
                  Database connectivity test
                </div>
              </div>

              {/* API Services */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold mb-2">API Services</h3>
                <div 
                  data-testid="apis-status"
                  className={`${getStatusColor(status.apis)}`}
                >
                  {getStatusIcon(status.apis)} {status.apis}
                </div>
                <div className="text-sm text-gray-600 mt-2">
                  External API availability
                </div>
              </div>
            </div>

            {/* Environment Info */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold mb-2">Environment Information</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Node Environment:</strong> {import.meta.env.NODE_ENV || 'development'}
                </div>
                <div>
                  <strong>Build Mode:</strong> {import.meta.env.MODE || 'development'}
                </div>
                <div>
                  <strong>Base URL:</strong> {import.meta.env.BASE_URL || '/'}
                </div>
                <div>
                  <strong>Site URL:</strong> {import.meta.env.SITE_URL || 'localhost'}
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex space-x-4">
              <button
                onClick={checkEnvironment}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Recheck Environment
              </button>
              
              <button
                onClick={() => window.location.href = '/'}
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                Return to App
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnvironmentTestPage;
