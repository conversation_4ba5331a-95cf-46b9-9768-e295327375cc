{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon index.js", "start": "node index.js", "test": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:report": "playwright show-report"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@serverless-jwt/netlify": "^0.2.1", "@supabase/supabase-js": "^2.50.0", "aws-sdk": "^2.1692.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "class-variance-authority": "^0.7.1", "cloudinary": "^1.41.3", "clsx": "^2.1.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "discord.js": "^14.19.3", "dotenv": "^16.5.0", "express": "^4.21.0", "flat": "^6.0.1", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "method-override": "^3.0.0", "mongoose": "^8.7.0", "multer": "^2.0.1", "node-fetch": "^2.7.0", "nodemailer": "^6.10.1", "nodemon": "^3.1.7", "passport": "^0.7.0", "passport-local": "^1.0.0", "passport-local-mongoose": "^8.0.0", "pdfkit": "^0.17.1", "react-minimal-pie-chart": "^9.1.0", "react-signature-canvas": "^1.1.0-alpha.2", "shadcn-ui": "^0.9.5", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "web-push": "^3.6.7"}, "packageManager": "pnpm@9.4.0+sha512.f549b8a52c9d2b8536762f99c0722205efc5af913e77835dbccc3b0b0b2ca9e7dc8022b78062c17291c48e88749c70ce88eb5a74f1fa8c4bf5e18bb46c8bd83a", "devDependencies": {"@playwright/test": "^1.53.0", "@types/node": "^22.15.21", "supabase": "^2.22.6"}}