import React, { useState } from 'react';
import { toast } from 'react-toastify';
import { supabase } from '../../utils/supabase/supabase.utils';

/**
 * CompanyDetails component - Display and edit business entity information
 * Maintains gamified theming while showing compliance data
 */
const CompanyDetails = ({ company, onUpdate, canEdit = false }) => {
  const [editMode, setEditMode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [companyData, setCompanyData] = useState({
    legal_name: company?.legal_name || '',
    doing_business_as: company?.doing_business_as || '',
    business_description: company?.business_description || '',
    website_url: company?.website_url || '',
    primary_address: company?.primary_address || {},
    primary_email: company?.primary_email || '',
    primary_phone: company?.primary_phone || ''
  });

  const handleEdit = () => {
    setEditMode(true);
    setCompanyData({
      legal_name: company.legal_name,
      doing_business_as: company.doing_business_as || '',
      business_description: company.business_description || '',
      website_url: company.website_url || '',
      primary_address: company.primary_address || {},
      primary_email: company.primary_email,
      primary_phone: company.primary_phone || ''
    });
  };

  const handleCancel = () => {
    setEditMode(false);
    setCompanyData({
      legal_name: company.legal_name,
      doing_business_as: company.doing_business_as || '',
      business_description: company.business_description || '',
      website_url: company.website_url || '',
      primary_address: company.primary_address || {},
      primary_email: company.primary_email,
      primary_phone: company.primary_phone || ''
    });
  };

  const handleSave = async () => {
    try {
      setLoading(true);

      const response = await fetch(`/.netlify/functions/companies/${company.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        },
        body: JSON.stringify(companyData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update company');
      }

      toast.success('Business entity updated successfully');
      setEditMode(false);
      onUpdate();
    } catch (error) {
      console.error('Error updating company:', error);
      toast.error(error.message || 'Failed to update business entity');
    } finally {
      setLoading(false);
    }
  };

  const updateField = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setCompanyData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setCompanyData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const getCompanyTypeDisplay = (type) => {
    const types = {
      llc: 'LLC',
      corporation: 'Corporation',
      partnership: 'Partnership',
      sole_proprietorship: 'Sole Proprietorship'
    };
    return types[type] || type;
  };

  const formatAddress = (address) => {
    if (!address || typeof address !== 'object') return 'No address provided';
    const { street, city, state, zip, country } = address;
    return `${street || ''}, ${city || ''}, ${state || ''} ${zip || ''} ${country || ''}`.replace(/,\s*,/g, ',').trim();
  };

  if (!company) {
    return (
      <div className="no-company">
        <p>No business entity registered</p>
      </div>
    );
  }

  return (
    <div className="company-details">
      <div className="company-header">
        <div className="company-status">
          <span className="status-badge active">🏰 Established Alliance</span>
          <span className="compliance-badge">✅ Compliant</span>
        </div>
        {canEdit && !editMode && (
          <button onClick={handleEdit} className="edit-button">
            Edit Details
          </button>
        )}
      </div>

      {editMode ? (
        <div className="edit-company-form">
          <div className="form-section">
            <h4>📋 Business Information</h4>
            
            <div className="form-group">
              <label>Legal Business Name</label>
              <input
                type="text"
                value={companyData.legal_name}
                onChange={(e) => updateField('legal_name', e.target.value)}
                placeholder="Legal business name"
              />
            </div>

            <div className="form-group">
              <label>Doing Business As (DBA)</label>
              <input
                type="text"
                value={companyData.doing_business_as}
                onChange={(e) => updateField('doing_business_as', e.target.value)}
                placeholder="DBA name"
              />
            </div>

            <div className="form-group">
              <label>Business Description</label>
              <textarea
                value={companyData.business_description}
                onChange={(e) => updateField('business_description', e.target.value)}
                placeholder="Describe your business"
                rows="3"
              />
            </div>

            <div className="form-group">
              <label>Website</label>
              <input
                type="url"
                value={companyData.website_url}
                onChange={(e) => updateField('website_url', e.target.value)}
                placeholder="https://yourwebsite.com"
              />
            </div>
          </div>

          <div className="form-section">
            <h4>📍 Contact Information</h4>
            
            <div className="form-group">
              <label>Street Address</label>
              <input
                type="text"
                value={companyData.primary_address.street || ''}
                onChange={(e) => updateField('primary_address.street', e.target.value)}
                placeholder="Street address"
              />
            </div>

            <div className="form-row">
              <div className="form-group">
                <label>City</label>
                <input
                  type="text"
                  value={companyData.primary_address.city || ''}
                  onChange={(e) => updateField('primary_address.city', e.target.value)}
                  placeholder="City"
                />
              </div>
              <div className="form-group">
                <label>State</label>
                <input
                  type="text"
                  value={companyData.primary_address.state || ''}
                  onChange={(e) => updateField('primary_address.state', e.target.value)}
                  placeholder="State"
                />
              </div>
              <div className="form-group">
                <label>ZIP</label>
                <input
                  type="text"
                  value={companyData.primary_address.zip || ''}
                  onChange={(e) => updateField('primary_address.zip', e.target.value)}
                  placeholder="ZIP"
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label>Business Email</label>
                <input
                  type="email"
                  value={companyData.primary_email}
                  onChange={(e) => updateField('primary_email', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="form-group">
                <label>Business Phone</label>
                <input
                  type="tel"
                  value={companyData.primary_phone}
                  onChange={(e) => updateField('primary_phone', e.target.value)}
                  placeholder="******-123-4567"
                />
              </div>
            </div>
          </div>

          <div className="form-actions">
            <button 
              onClick={handleSave} 
              disabled={loading}
              className="save-button"
            >
              {loading ? 'Saving...' : 'Save Changes'}
            </button>
            <button onClick={handleCancel} className="cancel-button">
              Cancel
            </button>
          </div>
        </div>
      ) : (
        <div className="company-info">
          <div className="info-section">
            <h4>📋 Business Information</h4>
            <div className="info-grid">
              <div className="info-item">
                <span className="label">Legal Name:</span>
                <span className="value">{company.legal_name}</span>
              </div>
              {company.doing_business_as && (
                <div className="info-item">
                  <span className="label">DBA:</span>
                  <span className="value">{company.doing_business_as}</span>
                </div>
              )}
              <div className="info-item">
                <span className="label">Tax ID:</span>
                <span className="value">{company.tax_id}</span>
              </div>
              <div className="info-item">
                <span className="label">Business Type:</span>
                <span className="value">{getCompanyTypeDisplay(company.company_type)}</span>
              </div>
              {company.incorporation_state && (
                <div className="info-item">
                  <span className="label">Incorporated In:</span>
                  <span className="value">{company.incorporation_state}, {company.incorporation_country}</span>
                </div>
              )}
              {company.business_description && (
                <div className="info-item full-width">
                  <span className="label">Description:</span>
                  <span className="value">{company.business_description}</span>
                </div>
              )}
              {company.website_url && (
                <div className="info-item">
                  <span className="label">Website:</span>
                  <span className="value">
                    <a href={company.website_url} target="_blank" rel="noopener noreferrer">
                      {company.website_url}
                    </a>
                  </span>
                </div>
              )}
            </div>
          </div>

          <div className="info-section">
            <h4>📍 Contact Information</h4>
            <div className="info-grid">
              <div className="info-item full-width">
                <span className="label">Address:</span>
                <span className="value">{formatAddress(company.primary_address)}</span>
              </div>
              <div className="info-item">
                <span className="label">Email:</span>
                <span className="value">
                  <a href={`mailto:${company.primary_email}`}>{company.primary_email}</a>
                </span>
              </div>
              {company.primary_phone && (
                <div className="info-item">
                  <span className="label">Phone:</span>
                  <span className="value">
                    <a href={`tel:${company.primary_phone}`}>{company.primary_phone}</a>
                  </span>
                </div>
              )}
            </div>
          </div>

          <div className="info-section">
            <h4>📊 Compliance Status</h4>
            <div className="compliance-grid">
              <div className="compliance-item">
                <span className="status-icon">✅</span>
                <span className="status-text">Tax ID Registered</span>
              </div>
              <div className="compliance-item">
                <span className="status-icon">✅</span>
                <span className="status-text">Business Entity Active</span>
              </div>
              <div className="compliance-item">
                <span className="status-icon">✅</span>
                <span className="status-text">Financial Reporting Ready</span>
              </div>
              <div className="compliance-item">
                <span className="status-icon">✅</span>
                <span className="status-text">1099 Generation Enabled</span>
              </div>
            </div>
          </div>

          <div className="info-section">
            <h4>📈 Business Capabilities</h4>
            <div className="capabilities-list">
              <div className="capability-item">
                <span className="capability-icon">💰</span>
                <div className="capability-text">
                  <strong>Commission Payments</strong>
                  <p>Process tax-compliant commission payments to sales team</p>
                </div>
              </div>
              <div className="capability-item">
                <span className="capability-icon">🔄</span>
                <div className="capability-text">
                  <strong>Recurring Billing</strong>
                  <p>Manage monthly talent fees and subscription services</p>
                </div>
              </div>
              <div className="capability-item">
                <span className="capability-icon">📊</span>
                <div className="capability-text">
                  <strong>Financial Reporting</strong>
                  <p>Generate professional business reports and tax documents</p>
                </div>
              </div>
              <div className="capability-item">
                <span className="capability-icon">🛡️</span>
                <div className="capability-text">
                  <strong>Legal Protection</strong>
                  <p>Business entity provides liability protection and compliance</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CompanyDetails;
