import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, Button } from '@heroui/react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import LoadingAnimation from '../../components/layout/LoadingAnimation';

/**
 * MyTeams Section Component
 * Shows user's teams/alliances with management options
 * Part of the experimental navigation system
 */
const MyTeams = ({ canvasId, sectionId }) => {
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();
  const [teams, setTeams] = useState([]);
  const [loading, setLoading] = useState(true);
  const [companies, setCompanies] = useState([]);

  useEffect(() => {
    if (currentUser) {
      fetchUserTeams();
      fetchCompanies();
    }
  }, [currentUser]);

  const fetchUserTeams = async () => {
    try {
      // First get team memberships
      const { data: memberships, error: membershipError } = await supabase
        .from('team_members')
        .select('*')
        .eq('user_id', currentUser.id);

      if (membershipError) {
        console.error('Error fetching team memberships:', membershipError);
        setTeams([]);
        return;
      }

      if (!memberships || memberships.length === 0) {
        setTeams([]);
        return;
      }

      // Then get team details separately
      const teamIds = memberships.map(m => m.team_id);
      const { data: teamsData, error: teamsError } = await supabase
        .from('teams')
        .select(`
          id,
          name,
          description,
          is_business_entity,
          alliance_type,
          company_id,
          created_at
        `)
        .in('id', teamIds);

      if (teamsError) {
        console.error('Error fetching teams data:', teamsError);
        setTeams([]);
        return;
      }

      // Combine the data
      const combinedData = memberships.map(membership => ({
        ...membership,
        teams: teamsData.find(team => team.id === membership.team_id)
      })).filter(item => item.teams); // Filter out any teams that couldn't be found

      setTeams(combinedData);
    } catch (error) {
      console.error('Error fetching teams:', error);
      setTeams([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchCompanies = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;

      if (authToken) {
        const response = await fetch('/.netlify/functions/companies', {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const result = await response.json();
          setCompanies(result.companies || []);
        }
      }
    } catch (error) {
      console.error('Error fetching companies:', error);
    }
  };

  const getTeamTypeIcon = (allianceType) => {
    switch (allianceType) {
      case 'established': return '🏰';
      case 'emerging': return '🌱';
      case 'solo': return '⭐';
      default: return '👥';
    }
  };

  const getTeamTypeLabel = (allianceType) => {
    switch (allianceType) {
      case 'established': return 'Established Alliance';
      case 'emerging': return 'Emerging Alliance';
      case 'solo': return 'Solo Venture';
      default: return 'Team';
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-8">
            <LoadingAnimation />
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-6 space-y-6"
    >
      {/* Section Header */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-white mb-2">👥 My Teams & Alliances</h2>
        <p className="text-white/70">
          Manage your teams, alliances, and business entities
        </p>
      </div>

      {/* Quick Actions */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardBody className="p-6">
          <div className="flex flex-wrap gap-4 justify-center">
            <Button
              color="primary"
              variant="solid"
              size="lg"
              startContent="🏰"
              onClick={() => navigate('/alliances/create')}
            >
              Create Alliance
            </Button>
            <Button
              color="secondary"
              variant="solid"
              size="lg"
              startContent="🤝"
              onClick={() => navigate('/alliances')}
            >
              Join Alliance
            </Button>
            <Button
              color="success"
              variant="solid"
              size="lg"
              startContent="🏢"
              onClick={() => navigate('/alliances/create')}
            >
              Register Business
            </Button>
          </div>
        </CardBody>
      </Card>

      {/* Teams List */}
      {teams.length > 0 ? (
        <div className="grid gap-4">
          {teams.map((teamMember) => {
            const team = teamMember.teams;
            const company = companies.find(c => c.id === team.company_id);

            return (
              <motion.div
                key={team.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Card className="bg-white/10 backdrop-blur-md border-white/20 hover:bg-white/20 transition-all cursor-pointer">
                  <CardBody className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="text-4xl">
                          {getTeamTypeIcon(team.alliance_type)}
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-white">{team.name}</h3>
                          <p className="text-white/70 text-sm">
                            {getTeamTypeLabel(team.alliance_type)}
                          </p>
                          {team.description && (
                            <p className="text-white/60 text-sm mt-1">
                              {team.description}
                            </p>
                          )}
                          {company && (
                            <div className="flex items-center mt-2">
                              <span className="text-green-400 text-sm">🏢 {company.legal_name}</span>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex flex-col space-y-2">
                        <div className="flex items-center space-x-2">
                          {teamMember.is_admin && (
                            <span className="bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded text-xs">
                              👑 Admin
                            </span>
                          )}
                          {team.is_business_entity && (
                            <span className="bg-green-500/20 text-green-300 px-2 py-1 rounded text-xs">
                              🏢 Business
                            </span>
                          )}
                        </div>

                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            color="primary"
                            variant="flat"
                            onClick={() => navigate(`/teams/${team.id}`)}
                          >
                            View
                          </Button>
                          {teamMember.is_admin && (
                            <Button
                              size="sm"
                              color="secondary"
                              variant="flat"
                              onClick={() => navigate(`/teams/${team.id}/manage`)}
                            >
                              Manage
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </motion.div>
            );
          })}
        </div>
      ) : (
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-8 text-center">
            <div className="text-6xl mb-4">🏰</div>
            <h3 className="text-2xl font-bold text-white mb-4">No Teams Yet</h3>
            <p className="text-white/70 mb-6">
              You haven't joined any teams or alliances yet. Create your first alliance or join an existing one to get started!
            </p>
            <div className="flex gap-4 justify-center">
              <Button
                color="primary"
                size="lg"
                startContent="🏰"
                onClick={() => navigate('/alliances/create')}
              >
                Create Your Alliance
              </Button>
              <Button
                color="secondary"
                variant="bordered"
                size="lg"
                startContent="🔍"
                onClick={() => navigate('/alliances')}
              >
                Discover Alliances
              </Button>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Business Entities Summary */}
      {companies.length > 0 && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-6">
            <h3 className="text-xl font-bold text-white mb-4">🏢 Business Entities</h3>
            <div className="grid gap-3">
              {companies.map((company) => (
                <div key={company.id} className="flex items-center justify-between bg-white/5 rounded-lg p-3">
                  <div>
                    <h4 className="text-white font-medium">{company.legal_name}</h4>
                    <p className="text-white/60 text-sm">{company.company_type.toUpperCase()} • {company.tax_id}</p>
                  </div>
                  <Button
                    size="sm"
                    color="success"
                    variant="flat"
                    onClick={() => navigate(`/companies/${company.id}/manage`)}
                  >
                    Manage
                  </Button>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      )}
    </motion.div>
  );
};

export default MyTeams;
