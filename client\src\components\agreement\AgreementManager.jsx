import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import LoadingAnimation from '../layout/LoadingAnimation';
import PDFPreview from './PDFPreview';
import SignatureCanvas from './SignatureCanvas';
import AgreementButtons from './AgreementButtons';
import { markdownToHTML } from '../../utils/pdf/pdfGenerator';
import VersionHistoryModal from './VersionHistoryModal';
import { logProjectActivity } from '../../utils/activity-logger';
import { generateAgreement, regenerateAgreement as regenerateAgreementUtil } from '../../utils/agreement';
import { enhancedAgreementGenerator } from '../../utils/agreement/enhancedAgreementGenerator';


/**
 * AgreementManager Component
 *
 * A component for managing project agreements, including:
 * - Viewing agreement details
 * - Signing agreements
 * - Viewing agreement history
 * - Downloading agreements as PDF
 */
const AgreementManager = ({ projectId }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [regenerating, setRegenerating] = useState(false);
  const [agreements, setAgreements] = useState([]);
  const [currentAgreement, setCurrentAgreement] = useState(null);
  const [showSignatureModal, setShowSignatureModal] = useState(false);
  const [signatureData, setSignatureData] = useState(null);
  const [fullName, setFullName] = useState(currentUser?.user_metadata?.full_name || '');
  const [projectName, setProjectName] = useState('');
  const [showPdfPreview, setShowPdfPreview] = useState(false);
  const [userRole, setUserRole] = useState(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [selectedAgreement, setSelectedAgreement] = useState(null);

  // Fetch agreements and user role
  const fetchData = async () => {
    console.log('Fetching agreement data...');
    if (!projectId || !currentUser) {
      console.log('Missing projectId or currentUser, aborting fetch');
      return;
    }

    try {
      console.log('Setting loading state and fetching data for project:', projectId);
      setLoading(true);

      // Fetch project details to get the name
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select('name')
        .eq('id', projectId)
        .single();

      if (projectError) {
        console.error('Error fetching project details:', projectError);
      } else {
        setProjectName(projectData.name || '');
      }

      // Check user's role in the project
      const { data: contributor, error: contributorError } = await supabase
        .from('project_contributors')
        .select('*')
        .eq('project_id', projectId)
        .eq('user_id', currentUser.id)
        .single();

      if (contributorError && contributorError.code !== 'PGRST116') {
        throw contributorError;
      }

      if (contributor) {
        setUserRole(contributor.permission_level);
        setIsAdmin(contributor.permission_level === 'Owner' || contributor.permission_level === 'Admin');
      }

      // Fetch all agreements for the project
      const { data: agreementsData, error: agreementsError } = await supabase
        .from('contributor_agreements')
        .select(`
          *,
          project_contributors!inner(
            id,
            user_id,
            permission_level,
            display_name,
            email
          )
        `)
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      // Also fetch user details separately
      const userIds = agreementsData?.map(agreement => agreement.project_contributors?.user_id).filter(Boolean) || [];
      let userData = {};

      if (userIds.length > 0) {
        const { data: users, error: usersError } = await supabase
          .from('users')
          .select('id, display_name, email, avatar_url')
          .in('id', userIds);

        if (!usersError && users) {
          userData = users.reduce((acc, user) => {
            acc[user.id] = user;
            return acc;
          }, {});
        }
      }

      if (agreementsError) throw agreementsError;

      // Process agreements data
      const processedAgreements = agreementsData.map(agreement => {
        const contributorId = agreement.project_contributors?.user_id;
        const user = userData[contributorId] || {
          display_name: agreement.project_contributors?.display_name || 'Unknown User',
          email: agreement.project_contributors?.email || 'No email',
          avatar_url: null
        };

        return {
          ...agreement,
          contributor: agreement.project_contributors,
          user: user
        };
      });

      console.log(`Found ${processedAgreements.length} agreements for this project`);
      setAgreements(processedAgreements);

      // Find the current user's agreement
      const userAgreement = processedAgreements.find(
        agreement => agreement.contributor?.user_id === currentUser.id
      );

      if (userAgreement) {
        console.log('Found agreement for current user:', userAgreement.id, 'Version:', userAgreement.version);
        console.log('Agreement text preview:', userAgreement.agreement_text?.substring(0, 100) + '...');
        setCurrentAgreement(userAgreement);

        // If the agreement has a signature, set the signature data
        if (userAgreement.signature_data?.signature) {
          console.log('Agreement has signature data');
          setSignatureData(userAgreement.signature_data.signature);
          setFullName(userAgreement.signature_data.full_name || fullName);
        } else {
          console.log('Agreement has no signature data');
        }
      } else if (processedAgreements.length > 0) {
        console.log('No agreement found for current user, using template');
        // If there's no agreement for the current user but there are other agreements,
        // use the first one as a template but mark it as a new agreement for the current user
        const templateAgreement = { ...processedAgreements[0] };
        templateAgreement.id = null; // Clear ID to indicate it's a new agreement
        templateAgreement.status = 'pending';
        templateAgreement.signature_data = null;
        templateAgreement.signed_at = null;
        setCurrentAgreement(templateAgreement);
      } else {
        console.log('No agreements found for this project');
      }

    } catch (error) {
      console.error('Error fetching agreements:', error);
      toast.error('Failed to load agreements');
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, [projectId, currentUser, fullName]);

  // Handle signature save
  const handleSignatureSave = (dataURL) => {
    setSignatureData(dataURL);
    setShowSignatureModal(false);
  };

  // Sign agreement
  const signAgreement = async () => {
    if (!projectId || !currentUser || !signatureData || !currentAgreement) {
      toast.error('Missing required information for signing');
      return;
    }

    if (!fullName.trim()) {
      toast.error('Full name is required');
      return;
    }

    setLoading(true);

    try {
      // First, check if the agreement exists and get the contributor_id
      const { data: contributorData, error: contributorError } = await supabase
        .from('project_contributors')
        .select('id')
        .eq('project_id', projectId)
        .eq('user_id', currentUser.id)
        .single();

      if (contributorError) {
        console.error('Error finding contributor:', contributorError);
        toast.error('Could not find your contributor record');
        setLoading(false);
        return;
      }

      // Now check if the agreement exists
      const { data: agreementData, error: agreementError } = await supabase
        .from('contributor_agreements')
        .select('id')
        .eq('project_id', projectId)
        .eq('contributor_id', contributorData.id)
        .single();

      if (agreementError && agreementError.code !== 'PGRST116') {
        console.error('Error checking agreement:', agreementError);
        toast.error('Error checking agreement status');
        setLoading(false);
        return;
      }

      // If agreement doesn't exist, create it first
      if (agreementError && agreementError.code === 'PGRST116') {
        // Create a new agreement
        const { data: newAgreement, error: createError } = await supabase
          .from('contributor_agreements')
          .insert({
            project_id: projectId,
            contributor_id: contributorData.id,
            agreement_text: currentAgreement.agreement_text,
            version: 1,
            status: 'pending',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (createError) {
          console.error('Error creating agreement:', createError);
          toast.error('Failed to create agreement');
          setLoading(false);
          return;
        }

        // Update the current agreement with the new one
        setCurrentAgreement(newAgreement);

        // Now update the newly created agreement with signature
        const { error: updateError } = await supabase
          .from('contributor_agreements')
          .update({
            signature_data: {
              signature: signatureData,
              signed_by: currentUser.id,
              signed_at: new Date().toISOString(),
              full_name: fullName
            },
            status: 'signed',
            signed_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            version: 1
          })
          .eq('id', newAgreement.id);
      } else {
        // Update existing agreement with signature
        const { error: updateError } = await supabase
          .from('contributor_agreements')
          .update({
            signature_data: {
              signature: signatureData,
              signed_by: currentUser.id,
              signed_at: new Date().toISOString(),
              full_name: fullName
            },
            status: 'signed',
            signed_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            version: currentAgreement.version || 1 // Ensure version is set
          })
          .eq('id', currentAgreement.id);

        if (updateError) throw updateError;
      }

      // Update local state
      const updatedAgreement = {
        ...currentAgreement,
        signature_data: {
          signature: signatureData,
          signed_by: currentUser.id,
          signed_at: new Date().toISOString(),
          full_name: fullName
        },
        status: 'signed',
        signed_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        version: currentAgreement.version || 1
      };

      setCurrentAgreement(updatedAgreement);

      // Log activity
      await logProjectActivity(
        projectId,
        currentUser.id,
        'agreement_signed',
        {
          agreement_id: currentAgreement.id,
          version: currentAgreement.version || 1
        }
      );

      // Send email notification
      try {
        const response = await fetch('/.netlify/functions/agreement-notifications', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'sign',
            agreementId: currentAgreement.id
          })
        });

        if (!response.ok) {
          console.error('Error sending notification:', await response.text());
        }
      } catch (notificationError) {
        console.error('Failed to send notification:', notificationError);
      }

      toast.success('Agreement signed successfully');
    } catch (error) {
      console.error('Error signing agreement:', error);
      toast.error('Failed to sign agreement');
    } finally {
      setLoading(false);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Update agreement text
  const updateAgreementText = async (agreementId, newText) => {
    if (!agreementId || !newText) {
      toast.error('Missing required information for update');
      return;
    }

    setLoading(true);

    try {
      // Get the current agreement
      const { data: currentData, error: fetchError } = await supabase
        .from('contributor_agreements')
        .select('*')
        .eq('id', agreementId)
        .single();

      if (fetchError) throw fetchError;

      // Prepare previous versions array
      const previousVersions = currentData.previous_versions || [];

      // Add current version to previous versions
      previousVersions.push({
        version: currentData.version || 1,
        agreement_text: currentData.agreement_text,
        updated_at: currentData.updated_at,
        updated_by: currentUser.id,
        status: currentData.status
      });

      // Update the agreement with new text and increment version
      const { error: updateError } = await supabase
        .from('contributor_agreements')
        .update({
          agreement_text: newText,
          version: (currentData.version || 1) + 1,
          previous_versions: previousVersions,
          status: 'pending', // Reset to pending since it needs to be signed again
          updated_at: new Date().toISOString(),
          signature_data: null, // Clear signature since it's a new version
          signed_at: null
        })
        .eq('id', agreementId);

      if (updateError) throw updateError;

      // Log activity
      await logProjectActivity(
        projectId,
        currentUser.id,
        'agreement_updated',
        {
          agreement_id: agreementId,
          version: (currentData.version || 1) + 1
        }
      );

      // Send email notification
      try {
        const response = await fetch('/.netlify/functions/agreement-notifications', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'update',
            agreementId: agreementId
          })
        });

        if (!response.ok) {
          console.error('Error sending notification:', await response.text());
        }
      } catch (notificationError) {
        console.error('Failed to send notification:', notificationError);
      }

      toast.success('Agreement updated successfully');

      // Refresh agreements
      const { data: agreementsData, error: agreementsError } = await supabase
        .from('contributor_agreements')
        .select(`
          *,
          project_contributors!inner(
            id,
            user_id,
            permission_level,
            display_name,
            email
          )
        `)
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (agreementsError) throw agreementsError;

      // Process agreements data
      const processedAgreements = agreementsData.map(agreement => {
        return {
          ...agreement,
          contributor: agreement.project_contributors,
          user: {
            display_name: agreement.project_contributors?.display_name || 'Unknown User',
            email: agreement.project_contributors?.email || 'No email',
            avatar_url: null
          }
        };
      });

      setAgreements(processedAgreements);

      // Update current agreement if it was the one that was updated
      if (currentAgreement && currentAgreement.id === agreementId) {
        const updatedAgreement = processedAgreements.find(a => a.id === agreementId);
        if (updatedAgreement) {
          setCurrentAgreement(updatedAgreement);
          setSignatureData(null); // Clear signature since it's a new version
        }
      }
    } catch (error) {
      console.error('Error updating agreement:', error);
      toast.error('Failed to update agreement');
    } finally {
      setLoading(false);
    }
  };

  // Create new agreement for current user
  const createNewAgreement = async () => {
    if (!projectId || !currentUser) {
      toast.error('Missing required information');
      return;
    }

    setLoading(true);

    try {
      // First, check if the user is a contributor
      const { data: contributorData, error: contributorError } = await supabase
        .from('project_contributors')
        .select('id')
        .eq('project_id', projectId)
        .eq('user_id', currentUser.id)
        .single();

      if (contributorError) {
        console.error('Error finding contributor:', contributorError);
        toast.error('Could not find your contributor record');
        setLoading(false);
        return;
      }

      // Generate agreement text
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .single();

      if (projectError) throw projectError;

      // Fetch contributors
      const { data: contributors, error: contributorsError } = await supabase
        .from('project_contributors')
        .select('*')
        .eq('project_id', projectId);

      if (contributorsError) throw contributorsError;

      // Format date for the agreement header
      const today = new Date();
      const month = today.toLocaleDateString('en-US', { month: 'long' });
      const day = today.getDate();
      const year = today.getFullYear().toString();
      const headerDate = `${month} ${day}, ${year}`;

      // Get agreement template
      const agreementTemplate = await fetch('/example-cog-contributor-agreement.md').then(res => res.text());

      // Find project owner
      const owner = contributors.find(c => c.permission_level === 'Owner');
      const ownerName = owner?.display_name || 'Project Owner';
      const contributorName = currentUser?.user_metadata?.full_name || currentUser.email;

      // Get project milestones for the exhibits
      const { data: milestones, error: milestonesError } = await supabase
        .from('project_milestones')
        .select('*')
        .eq('project_id', projectId)
        .order('due_date', { ascending: true });

      if (milestonesError) {
        console.error('Error fetching milestones:', milestonesError);
      }

      // Generate exhibits based on project details
      const generateExhibits = () => {
        // Generate specifications text (Exhibit I)
        const specificationsText = `
### Project Type
${project.project_type || 'Software Development'}

### Project Description
${project.description || 'A collaborative project'}

### Technical Requirements
- Platform: ${project.platform || 'Web, Mobile, Desktop'}
- Technologies: ${project.technologies || 'To be determined based on project needs'}
- Compatibility: ${project.compatibility || 'Standard browser and device compatibility'}
`;

        // Generate milestones text (Exhibit II)
        let milestonesText = '### Project Milestones\n\n';

        if (milestones && milestones.length > 0) {
          milestonesText += 'The following milestones have been established for this project:\n\n';
          milestones.forEach((milestone, index) => {
            const dueDate = milestone.due_date ? new Date(milestone.due_date).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }) : 'To be determined';

            milestonesText += `**${index + 1}. ${milestone.title}**\n`;
            milestonesText += `   - Description: ${milestone.description || 'No description provided'}\n`;
            milestonesText += `   - Due Date: ${dueDate}\n`;
            milestonesText += `   - Status: ${milestone.status || 'Pending'}\n\n`;
          });
        } else {
          milestonesText += 'Milestones will be defined as the project progresses.\n\n';
        }

        return {
          specificationsText,
          milestonesText
        };
      };

      const { specificationsText, milestonesText } = generateExhibits();

      // Use the Royaltea agreement generator to create a fully customized agreement
      let agreementText;
      try {
        console.log('Using Royaltea agreement generator for new agreement...');

        // Create a new instance of the Royaltea agreement generator
        const generator = new AgreementGenerator();

        // Prepare data for the agreement generator
        const { companyInfo, options } = prepareAgreementData(
          project,
          contributors,
          { email: currentUser.email, user_metadata: { full_name: contributorName } },
          null, // royaltyModel
          contributorName
        );

        // Generate the customized agreement using the Royaltea generator
        agreementText = generator.generateAgreement(
          agreementTemplate,
          project,
          options
        );

        console.log('Agreement generated successfully with Royaltea generator');
      } catch (error) {
        console.error('Error generating agreement:', error);

        // Fallback to basic template replacement if the generator fails
        agreementText = agreementTemplate
          // Basic replacements
          .replace(/\[Project Name\]/g, project.name)
          .replace(/\[Date\]/g, headerDate)
          .replace(/\[Project Owner\]/g, ownerName)
          .replace(/\[Contributor\]/g, contributorName)
          .replace(/Village of The Ages/g, project.name)
          .replace(/village simulation game where players guide communities through historical progressions and manage resource-based challenges/g, project.description || 'A collaborative project')

          // Company information
          .replace(/City of Gamers Inc\./gi, project.company_name || ownerName || 'Project Owner')
          .replace(/City of Gamers/gi, project.company_name || ownerName || 'Project Owner')
          .replace(/\bCOG\b/gi, project.company_name || ownerName || 'Project Owner')
          .replace(/Gynell Journigan/gi, ownerName)

          // Location information
          .replace(/Florida/gi, project.state || 'Delaware')
          .replace(/Orlando/gi, project.city || 'Wilmington')
          .replace(/1205 43rd Street, Suite B, Orlando, Florida 32839/gi, project.address || '1209 N Orange St, Wilmington, DE 19801')
          .replace(/the applicable jurisdiction/gi, project.state || 'Delaware')

          // Contact information
          .replace(/billing@cogfuture\.com/gi, project.contact_email || ownerEmail || '<EMAIL>')
          .replace(/\[Project Owner Email\]/gi, project.contact_email || ownerEmail || '<EMAIL>')
          .replace(/\[Company Email\]/gi, project.contact_email || ownerEmail || '<EMAIL>')
          .replace(/\[Email\]/gi, project.contact_email || ownerEmail || '<EMAIL>')

          // Date placeholders
          .replace(/\[ \], 20\[__\]/g, headerDate)
          .replace(/Effective Date: .+?\n/g, `Effective Date: ${headerDate}\n`)
          .replace(/THIS CONTRIBUTOR AGREEMENT \(this "Agreement"\) is made as of .+? by and between/g,
                  `THIS CONTRIBUTOR AGREEMENT (this "Agreement") is made as of ${headerDate} by and between`)

          // Contributor information
          .replace(/\[_+\]/g, contributorName)
          .replace(/\[Contributor Name\]/g, contributorName)
          .replace(/\[CONTRIBUTOR NAME\]/g, contributorName.toUpperCase())

          // Clean up any remaining placeholders
          .replace(/\[__\]/g, '')
          .replace(/\[\s*\]/g, '')
          .replace(/\[.*?\]/g, '')

          // Exhibit replacements
          .replace(/## EXHIBIT I([\s\S]*?)## EXHIBIT II/g, `## EXHIBIT I\n### SPECIFICATIONS\n\n${specificationsText}\n\n## EXHIBIT II`)
          .replace(/## EXHIBIT II([\s\S]*?)IN WITNESS WHEREOF/g, `## EXHIBIT II\n### PRODUCT ROADMAP\n\n${milestonesText}\n\nIN WITNESS WHEREOF`);

        toast.error('Error generating agreement. Using basic template instead.');
      }

      // Verify that we're not losing any content from the template
      if (agreementText.length < agreementTemplate.length * 0.7) {
        console.error('Warning: Generated agreement appears to be missing content from the template');
      }

      // Create a new agreement
      const { data: newAgreement, error: createError } = await supabase
        .from('contributor_agreements')
        .insert({
          project_id: projectId,
          contributor_id: contributorData.id,
          agreement_text: agreementText,
          version: 1,
          status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (createError) {
        console.error('Error creating agreement:', createError);
        toast.error('Failed to create agreement');
        setLoading(false);
        return;
      }

      toast.success('New agreement created successfully');

      // Update the current agreement with the new one
      setCurrentAgreement(newAgreement);

      // Refresh agreements list
      const { data: agreementsData, error: agreementsError } = await supabase
        .from('contributor_agreements')
        .select(`
          *,
          project_contributors!inner(
            id,
            user_id,
            permission_level,
            display_name,
            email
          )
        `)
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (agreementsError) throw agreementsError;

      // Process agreements data
      const processedAgreements = agreementsData.map(agreement => {
        return {
          ...agreement,
          contributor: agreement.project_contributors,
          user: {
            display_name: agreement.project_contributors?.display_name || 'Unknown User',
            email: agreement.project_contributors?.email || 'No email',
            avatar_url: null
          }
        };
      });

      setAgreements(processedAgreements);

    } catch (error) {
      console.error('Error creating new agreement:', error);
      toast.error('Failed to create new agreement');
    } finally {
      setLoading(false);
    }
  };

  // Regenerate agreement
  const regenerateAgreement = async () => {
    console.log('Starting agreement regeneration...');
    if (!projectId || !currentUser) {
      toast.error('Missing required information');
      console.error('Missing projectId or currentUser');
      return;
    }

    setRegenerating(true);
    console.log('Project ID:', projectId);

    try {
      // Fetch project details
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .single();

      if (projectError) throw projectError;

      // Fetch contributors
      const { data: contributors, error: contributorsError } = await supabase
        .from('project_contributors')
        .select('*')
        .eq('project_id', projectId);

      if (contributorsError) throw contributorsError;

      // Fetch royalty model
      let royaltyModel = null;
      try {
        const { data, error } = await supabase
          .from('royalty_models')
          .select('*')
          .eq('project_id', projectId)
          .maybeSingle();

        if (!error && data) {
          royaltyModel = data;
        } else if (error && error.code !== 'PGRST116') {
          console.warn('Error fetching royalty model:', error);
        }
      } catch (modelError) {
        console.warn('Exception fetching royalty model:', modelError);
        // Continue without royalty model data
      }

      // Fetch agreement template
      const response = await fetch('/example-cog-contributor-agreement.md');
      if (!response.ok) {
        throw new Error(`Failed to load template: ${response.status}`);
      }
      const agreementTemplate = await response.text();

      // Fetch milestones
      let milestones = [];
      try {
        // Fetch from the milestones table
        const { data: projectMilestones, error: milestonesError } = await supabase
          .from('milestones')
          .select('*')
          .eq('project_id', projectId)
          .order('created_at', { ascending: true });

        if (!milestonesError && projectMilestones && projectMilestones.length > 0) {
          milestones = projectMilestones;
          console.log(`Found ${milestones.length} milestones for this project`);
        }
      } catch (milestonesError) {
        console.warn('Error fetching milestones:', milestonesError);
      }

      // Check if milestones exist and notify user if they don't
      if (!milestones || milestones.length === 0) {
        console.log('No milestones found for this project. A generic roadmap will be included in the agreement.');
        // Use console.log instead of toast.info to avoid potential issues
      }

      // Generate new agreement text
      const generateAgreement = async () => {
        console.log('Generating new agreement...');
        console.log('Project data:', project);
        console.log('Using royalty model:', royaltyModel);
        console.log('Milestones:', milestones?.length || 0);

        try {
          console.log('Using enhanced agreement generator...');
          // Use our enhanced agreement generator to create a fully customized agreement
          console.log('Project type from database:', project.project_type);

          const data = {
            project: {
              ...project,
              projectType: project.project_type || 'project', // Map project_type to projectType
              name: project.name || project.title || 'Project', // Handle both name and title fields
              description: project.description || 'A collaborative project',
              platform: project.platforms || project.distribution_platforms || '',
              technologies: project.technology_stack || project.engine || '',
              compatibility: project.platforms || ''
            },
            user: {
              owner: {
                name: contributors.find(c => c.permission_level === 'Owner')?.display_name || 'Project Owner',
                email: contributors.find(c => c.permission_level === 'Owner')?.email || '[Project Owner Email]',
                company: project.company_name || contributors.find(c => c.permission_level === 'Owner')?.display_name || 'Project Owner',
                address: project.company_address || '1209 N Orange St, Wilmington, DE 19801',
                city: project.city || 'Wilmington',
                state: project.company_state || project.state || 'Delaware',
                zip: '19801'
              }
            },
            contributors: contributors,
            currentUser: currentUser,
            royaltyModel: royaltyModel,
            milestones: milestones || [],
            fullName: fullName
          };

          console.log('Enhanced data for agreement generator:', JSON.stringify(data, null, 2));

          // Generate the agreement using our enhanced generator with current date
          const result = enhancedAgreementGenerator.generateAgreement(agreementTemplate, data, {
            agreementDate: new Date() // Always use current date for regenerated agreements
          });

          console.log('Agreement generated successfully with enhanced generator');
          return result;
        } catch (error) {
          console.error('Error using enhanced agreement generator:', error);
          console.log('Falling back to legacy agreement customizer');

          // Try again with our enhanced agreement generator but with more basic settings
          try {
            console.log('Trying enhanced agreement generator with fallback settings...');

            // Prepare data for the enhanced generator with more detailed logging
            console.log('Fallback: Project type from database:', project.project_type);

            const data = {
              project: {
                ...project,
                projectType: project.project_type || 'project', // Map project_type to projectType
                name: project.name || project.title || 'Project', // Handle both name and title fields
                description: project.description || 'A collaborative project',
                platform: project.platforms || project.distribution_platforms || '',
                technologies: project.technology_stack || project.engine || '',
                compatibility: project.platforms || ''
              },
              user: {
                owner: {
                  name: contributors.find(c => c.permission_level === 'Owner')?.display_name || 'Project Owner',
                  email: contributors.find(c => c.permission_level === 'Owner')?.email || '<EMAIL>',
                  company: project.company_name || contributors.find(c => c.permission_level === 'Owner')?.display_name || 'Project Owner',
                  address: project.company_address || '1209 N Orange St, Wilmington, DE 19801',
                  city: project.city || 'Wilmington',
                  state: project.company_state || project.state || 'Delaware',
                  zip: '19801'
                }
              },
              contributors: contributors,
              currentUser: currentUser,
              royaltyModel: royaltyModel,
              milestones: milestones || [],
              fullName: fullName
            };

            console.log('Fallback: Enhanced data for agreement generator:', JSON.stringify(data, null, 2));

            // Generate the agreement using our enhanced generator with current date
            const result = enhancedAgreementGenerator.generateAgreement(agreementTemplate, data, {
              agreementDate: new Date() // Always use current date for regenerated agreements
            });

            console.log('Agreement generated successfully with fallback settings');
            return result;
          } catch (fallbackError) {
            console.error('Error using enhanced agreement generator with fallback settings:', fallbackError);

            // If all else fails, use the most basic template replacement
            console.log('Using basic template replacement as last resort');
            const today = new Date();
            const headerDate = `${today.toLocaleDateString('en-US', { month: 'long' })} ${today.getDate()}, ${today.getFullYear()}`;
            const owner = contributors.find(c => c.permission_level === 'Owner');
            const ownerName = owner?.display_name || 'Project Owner';
            const ownerEmail = owner?.email || '<EMAIL>';

            // Create a simple agreement with basic placeholder replacement
            let basicAgreement = agreementTemplate
              // Basic replacements
              .replace(/\[Project Name\]/g, project.name)
              .replace(/\[Date\]/g, headerDate)
              .replace(/\[Project Owner\]/g, ownerName)
              .replace(/\[Contributor\]/g, fullName || currentUser?.user_metadata?.full_name || currentUser?.email || 'Contributor')
              .replace(/Village of The Ages/g, project.name)
              .replace(/village simulation game where players guide communities through historical progressions and manage resource-based challenges/g, project.description || 'A collaborative project')

              // Company information
              .replace(/City of Gamers Inc\./gi, project.company_name || ownerName || 'Project Owner')
              .replace(/City of Gamers/gi, project.company_name || ownerName || 'Project Owner')
              .replace(/\bCOG\b/gi, project.company_name || ownerName || 'Project Owner')
              .replace(/Gynell Journigan/gi, ownerName)

              // Location information
              .replace(/Florida/gi, project.state || 'Delaware')
              .replace(/Orlando/gi, project.city || 'Wilmington')
              .replace(/1205 43rd Street, Suite B, Orlando, Florida 32839/gi, project.address || '1209 N Orange St, Wilmington, DE 19801')
              .replace(/the applicable jurisdiction/gi, project.state || 'Delaware')

              // Contact information
              .replace(/billing@cogfuture\.com/gi, project.contact_email || ownerEmail || '<EMAIL>')
              .replace(/\[Project Owner Email\]/gi, project.contact_email || ownerEmail || '<EMAIL>')
              .replace(/\[Company Email\]/gi, project.contact_email || ownerEmail || '<EMAIL>')
              .replace(/\[Email\]/gi, project.contact_email || ownerEmail || '<EMAIL>')

              // Date placeholders
              .replace(/\[ \], 20\[__\]/g, headerDate)
              .replace(/Effective Date: .+?\n/g, `Effective Date: ${headerDate}\n`)
              .replace(/THIS CONTRIBUTOR AGREEMENT \(this "Agreement"\) is made as of .+? by and between/g,
                      `THIS CONTRIBUTOR AGREEMENT (this "Agreement") is made as of ${headerDate} by and between`)

              // Contributor information
              .replace(/\[_+\]/g, fullName || currentUser?.user_metadata?.full_name || currentUser?.email || 'Contributor')
              .replace(/\[Contributor Name\]/g, fullName || currentUser?.user_metadata?.full_name || currentUser?.email || 'Contributor')
              .replace(/\[CONTRIBUTOR NAME\]/g, (fullName || currentUser?.user_metadata?.full_name || currentUser?.email || 'Contributor').toUpperCase())

              // Clean up any remaining placeholders
              .replace(/\[__\]/g, '')
              .replace(/\[\s*\]/g, '')
              .replace(/\[.*?\]/g, '');

            return basicAgreement;
          }
        }
      };

      // Generate the new agreement text
      const newAgreementText = await generateAgreement();

      // Find the contributor ID
      const { data: contributorData, error: contributorError } = await supabase
        .from('project_contributors')
        .select('id')
        .eq('project_id', projectId)
        .eq('user_id', currentUser.id)
        .single();

      if (contributorError) {
        console.error('Error finding contributor:', contributorError);
        toast.error('Could not find your contributor record');
        setRegenerating(false);
        return;
      }

      // Check if agreement exists
      const { data: agreementData, error: agreementError } = await supabase
        .from('contributor_agreements')
        .select('id')
        .eq('project_id', projectId)
        .eq('contributor_id', contributorData.id)
        .single();

      if (agreementError && agreementError.code !== 'PGRST116') {
        console.error('Error checking agreement:', agreementError);
        toast.error('Error checking agreement status');
        setRegenerating(false);
        return;
      }

      // Update or create agreement
      if (agreementError && agreementError.code === 'PGRST116') {
        console.log('No existing agreement found, creating new one...');
        // Create new agreement
        const { data: newAgreement, error: createError } = await supabase
          .from('contributor_agreements')
          .insert({
            project_id: projectId,
            contributor_id: contributorData.id,
            agreement_text: newAgreementText,
            version: 1,
            status: 'pending',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (createError) {
          console.error('Error creating agreement:', createError);
          toast.error('Failed to create agreement');
          setRegenerating(false);
          return;
        }

        console.log('New agreement created successfully:', newAgreement.id);
        setCurrentAgreement(newAgreement);
        toast.success('New agreement created successfully');
      } else {
        console.log('Existing agreement found, updating to new version...');
        console.log('Agreement ID:', agreementData.id);
        // Update existing agreement with new version
        const currentVersion = currentAgreement?.version || 1;
        const newVersion = currentVersion + 1;
        console.log(`Updating from version ${currentVersion} to ${newVersion}`);

        // Log the first 100 characters of the new agreement text
        console.log('New agreement text preview:', newAgreementText.substring(0, 100) + '...');

        // Store previous version data
        const { data: currentAgreementData, error: fetchError } = await supabase
          .from('contributor_agreements')
          .select('*')
          .eq('id', agreementData.id)
          .single();

        if (fetchError) throw fetchError;

        // Prepare previous versions array
        const previousVersions = currentAgreementData.previous_versions || [];

        // Add current version to previous versions
        previousVersions.push({
          version: currentAgreementData.version || 1,
          agreement_text: currentAgreementData.agreement_text,
          updated_at: currentAgreementData.updated_at,
          created_at: currentAgreementData.created_at,
          updated_by: currentUser.id,
          status: currentAgreementData.status,
          signature_data: currentAgreementData.signature_data,
          signed_at: currentAgreementData.signed_at
        });

        const { data: updatedAgreement, error: updateError } = await supabase
          .from('contributor_agreements')
          .update({
            agreement_text: newAgreementText,
            version: newVersion,
            status: 'pending', // Reset to pending since it's a new version
            signature_data: null, // Clear signature data
            signed_at: null,
            updated_at: new Date().toISOString(),
            created_at: new Date().toISOString(), // Update creation date for the new version
            previous_versions: previousVersions
          })
          .eq('id', agreementData.id)
          .select()
          .single();

        if (updateError) {
          console.error('Error updating agreement:', updateError);
          toast.error('Failed to update agreement');
          setRegenerating(false);
          return;
        }

        console.log('Agreement updated successfully:', updatedAgreement.id);
        setCurrentAgreement(updatedAgreement);
        setSignatureData(null); // Clear signature data in UI
        toast.success('Agreement updated to version ' + newVersion);
      }

      // Refresh agreements list with a slight delay to ensure database consistency
      console.log('Refreshing agreement data...');
      setTimeout(() => {
        fetchData();
      }, 500);
    } catch (error) {
      console.error('Error regenerating agreement:', error);
      toast.error('Failed to regenerate agreement');
    } finally {
      setRegenerating(false);
    }
  };

  // View version history
  const viewVersionHistory = (agreement) => {
    setSelectedAgreement(agreement);
    setShowVersionHistory(true);
  };

  // Get agreement status badge
  const getStatusBadge = (status) => {
    switch (status) {
      case 'signed':
        return <span className="status-badge signed">Signed</span>;
      case 'pending':
        return <span className="status-badge pending">Pending</span>;
      case 'rejected':
        return <span className="status-badge rejected">Rejected</span>;
      default:
        return <span className="status-badge">{status}</span>;
    }
  };

  if (loading) {
    return <LoadingAnimation />;
  }

  return (
    <div className="agreement-manager">
      {/* PDF Preview Modal */}
      {showPdfPreview && currentAgreement && (
        <PDFPreview
          agreementText={currentAgreement.agreement_text}
          metadata={{
            title: 'Contributor Agreement',
            projectName: projectName || 'Project', // Use project name from state
            date: formatDate(currentAgreement.created_at),
            filename: `${projectName || 'project'}_agreement_v${currentAgreement.version || 1}.pdf`,
            version: currentAgreement.version || 1,
            signature: currentAgreement.signature_data ? {
              name: currentAgreement.signature_data.full_name,
              date: formatDate(currentAgreement.signature_data.signed_at),
              image: currentAgreement.signature_data.signature
            } : null
          }}
          onClose={() => setShowPdfPreview(false)}
        />
      )}

      {/* Version History Modal */}
      {showVersionHistory && selectedAgreement && (
        <VersionHistoryModal
          agreement={selectedAgreement}
          onClose={() => {
            setShowVersionHistory(false);
            setSelectedAgreement(null);
          }}
          formatDate={formatDate}
        />
      )}

      {/* Signature Modal */}
      {showSignatureModal && (
        <div className="modal-overlay">
          <div className="signature-modal">
            <div className="modal-header">
              <h3>Digital Signature</h3>
              <button
                className="close-button"
                onClick={() => setShowSignatureModal(false)}
              >
                &times;
              </button>
            </div>
            <div className="modal-body">
              <SignatureCanvas
                onSave={handleSignatureSave}
                initialValue={signatureData}
              />
            </div>
          </div>
        </div>
      )}

      <h2 className="section-title">Project Agreements</h2>

      {/* Current User's Agreement */}
      {currentAgreement ? (
        <div className="current-agreement">
          <h3>Your Agreement</h3>
          <div className="agreement-card">
            <div className="agreement-header">
              <div className="agreement-info">
                <div className="agreement-title">Contributor Agreement</div>
                <div className="agreement-date">Created on {formatDate(currentAgreement.created_at)}</div>
                <div className="agreement-version">Version {currentAgreement.version || 1}</div>
              </div>
              <div className="agreement-status">
                {getStatusBadge(currentAgreement.status)}
              </div>
            </div>

            <AgreementButtons
              onView={() => setShowPdfPreview(true)}
              onSign={() => setShowSignatureModal(true)}
              onRegenerate={regenerateAgreement}
              canSign={currentAgreement.status !== 'signed'}
              regenerating={regenerating}
            />

            {currentAgreement.status !== 'signed' && (
              <div className="signature-section">
                <h4>Digital Signature</h4>
                <p className="signature-info">
                  By signing this agreement, you acknowledge that you have read,
                  understood, and agree to the terms and conditions outlined in the agreement.
                </p>

                <div className="form-group">
                  <label htmlFor="fullName">Full Name</label>
                  <input
                    type="text"
                    id="fullName"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    placeholder="Enter your full legal name"
                    className="form-control"
                  />
                </div>

                {signatureData ? (
                  <div className="signature-preview">
                    <div className="signature-image">
                      <img src={signatureData} alt="Your signature" />
                    </div>
                    <button
                      className="change-signature-button"
                      onClick={() => setShowSignatureModal(true)}
                    >
                      Change Signature
                    </button>
                  </div>
                ) : (
                  <button
                    className="add-signature-button"
                    onClick={() => setShowSignatureModal(true)}
                  >
                    <i className="bi bi-pen"></i> Add Your Signature
                  </button>
                )}

                <button
                  className="sign-agreement-button"
                  onClick={signAgreement}
                  disabled={!signatureData || !fullName.trim()}
                >
                  Sign Agreement
                </button>
              </div>
            )}

            {currentAgreement.status === 'signed' && (
              <div className="signed-info">
                <div className="signed-details">
                  <p>
                    <strong>Signed by:</strong> {currentAgreement.signature_data?.full_name}
                  </p>
                  <p>
                    <strong>Signed on:</strong> {formatDate(currentAgreement.signature_data?.signed_at)}
                  </p>
                </div>
                <div className="signature-display">
                  <img
                    src={currentAgreement.signature_data?.signature}
                    alt="Signature"
                    className="signature-image"
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="no-agreement">
          <p>You don't have an agreement for this project yet.</p>
          <button
            className="create-agreement-button"
            onClick={createNewAgreement}
          >
            <i className="bi bi-file-earmark-plus"></i> Create New Agreement
          </button>
        </div>
      )}

      {/* Admin section removed as requested */}
    </div>
  );
};

export default AgreementManager;
