import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import LoadingAnimation from '../../components/layout/LoadingAnimation';
import TeamProfileSection from '../../components/team/TeamProfileSection';
import UserSkillsList from '../../components/skills/UserSkillsList';
import SkillForm from '../../components/skills/SkillForm';
import VerificationForm from '../../components/skills/VerificationForm';

const ProfilePage = () => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [userData, setUserData] = useState({
    display_name: '',
    bio: '',
    avatar_url: '',
    certifications: [], // Read-only, earned through the platform
    awards: [], // Read-only, earned through the platform
    stats: {
      projects_completed: 0,
      contributions: 0,
      hours_tracked: 0
    },
    social_links: {
      github: '',
      twitter: '',
      linkedin: '',
      website: ''
    },
    preferences: {
      theme: 'light',
      notifications_enabled: true
    }
  });
  const [avatarFile, setAvatarFile] = useState(null);
  const [avatarPreview, setAvatarPreview] = useState('');

  // Skills management state
  const [showSkillForm, setShowSkillForm] = useState(false);
  const [showVerificationForm, setShowVerificationForm] = useState(false);
  const [selectedUserSkill, setSelectedUserSkill] = useState(null);

  // Load user data
  useEffect(() => {
    const fetchUserData = async () => {
      if (!currentUser) return;

      try {
        setLoading(true);

        // Get user data from Supabase
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', currentUser.id)
          .single();

        if (error) {
          throw error;
        }

        // Determine default avatar
        const defaultAvatar = data.is_premium ? '/default-avatar-crown.png' : '/default-avatar-specs.png';
        console.log('Default avatar path:', defaultAvatar);

        // Set user data with defaults for missing fields
        // Always prioritize the database display_name to prevent it from being reset
        setUserData({
          display_name: data.display_name || currentUser.user_metadata?.full_name || '',
          bio: data.bio || '',
          avatar_url: data.avatar_url || defaultAvatar,
          certifications: data.certifications || [],
          awards: data.awards || [],
          stats: data.stats || {
            projects_completed: 0,
            contributions: 0,
            hours_tracked: 0
          },
          social_links: data.social_links || {
            github: '',
            twitter: '',
            linkedin: '',
            website: ''
          },
          preferences: data.preferences || {
            theme: 'light',
            notifications_enabled: true
          }
        });

        // Log the display name to help with debugging
        console.log('Loaded display name:', data.display_name);

        // Set avatar preview
        const avatarUrl = data.avatar_url || (data.is_premium ? '/default-avatar-crown.png' : '/default-avatar-specs.png');
        setAvatarPreview(avatarUrl);
        console.log('Setting avatar preview to:', avatarUrl);
      } catch (error) {
        console.error('Error fetching user data:', error);
        toast.error('Failed to load profile data');
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [currentUser]);

  // Handle input changes
  const handleChange = (e) => {
    const { name, value } = e.target;

    // Handle nested objects
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setUserData({
        ...userData,
        [parent]: {
          ...userData[parent],
          [child]: value
        }
      });
    } else {
      setUserData({
        ...userData,
        [name]: value
      });
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e) => {
    const { name, checked } = e.target;
    const [parent, child] = name.split('.');

    setUserData({
      ...userData,
      [parent]: {
        ...userData[parent],
        [child]: checked
      }
    });
  };

  // Handle avatar file selection
  const handleAvatarChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Check file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Check file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('Image size should be less than 2MB');
      return;
    }

    setAvatarFile(file);

    // Create preview
    const reader = new FileReader();
    reader.onload = () => {
      setAvatarPreview(reader.result);
    };
    reader.readAsDataURL(file);
  };

  // Upload avatar to Supabase storage
  const uploadAvatar = async () => {
    if (!avatarFile || !currentUser) return null;

    try {
      // Create a unique file name
      const fileExt = avatarFile.name.split('.').pop();
      const fileName = `avatar-${currentUser.id}.${fileExt}`;
      const filePath = fileName; // Simplified path

      console.log('Uploading avatar with path:', filePath);

      // First, try to delete any existing avatar with the same name
      try {
        await supabase.storage
          .from('user-avatars')
          .remove([filePath]);
        console.log('Removed existing avatar if any');
      } catch (deleteError) {
        // Ignore delete errors, just log them
        console.log('No existing avatar to delete or error:', deleteError);
      }

      // Upload to Supabase Storage
      const { data, error: uploadError } = await supabase.storage
        .from('user-avatars')
        .upload(filePath, avatarFile, {
          cacheControl: '3600',
          upsert: true
        });

      if (uploadError) {
        console.error('Upload error details:', uploadError);
        throw uploadError;
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('user-avatars')
        .getPublicUrl(filePath);

      console.log('Upload successful, public URL:', urlData.publicUrl);

      // Add a timestamp to force browser to reload the image
      const cachedUrl = `${urlData.publicUrl}?t=${Date.now()}`;
      return cachedUrl;
    } catch (error) {
      console.error('Error uploading avatar:', error);
      toast.error(`Failed to upload profile picture: ${error.message || 'Unknown error'}`);
      return null;
    }
  };

  // Save profile data
  const handleSave = async () => {
    if (!currentUser) return;

    try {
      setSaving(true);

      // Upload avatar if a new one was selected
      let avatarUrl = userData.avatar_url;
      if (avatarFile) {
        const uploadedUrl = await uploadAvatar();
        if (uploadedUrl) {
          avatarUrl = uploadedUrl;
        }
      }

      // Update user data in Supabase
      const { error } = await supabase
        .from('users')
        .update({
          display_name: userData.display_name,
          bio: userData.bio,
          avatar_url: avatarUrl,
          social_links: userData.social_links,
          preferences: userData.preferences
        })
        .eq('id', currentUser.id);

      if (error) {
        throw error;
      }

      // Update the avatar URL in the state
      setUserData({
        ...userData,
        avatar_url: avatarUrl
      });

      toast.success('Profile updated successfully');

      // Reset the avatar file state
      setAvatarFile(null);
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  // Navigate to public profile
  const viewPublicProfile = () => {
    window.location.href = `/profile/${currentUser.id}`;
  };

  // Skill management handlers
  const handleAddSkill = () => {
    setSelectedUserSkill(null);
    setShowSkillForm(true);
  };

  const handleEditSkill = (userSkill) => {
    setSelectedUserSkill(userSkill);
    setShowSkillForm(true);
  };

  const handleAddVerification = (userSkill) => {
    setSelectedUserSkill(userSkill);
    setShowVerificationForm(true);
  };

  const handleSkillSaved = (savedSkill) => {
    toast.success('Skill saved successfully');
    // Force refresh of skills list
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  const handleVerificationSaved = (savedVerification) => {
    toast.success('Verification added successfully');
    // Force refresh of skills list
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  if (loading) {
    return <LoadingAnimation />;
  }

  return (
    <div className="profile-page">
      <div className="container">
        <div className="profile-header">
          <h1>Your Profile</h1>
          <p>Customize your profile and preferences</p>
          <div className="profile-header-buttons">
            <button
              type="button"
              className="btn btn-outline-primary view-public-profile-btn"
              onClick={viewPublicProfile}
            >
              <i className="bi bi-eye"></i> View Public Profile
            </button>
            <a
              href="/retro-profile"
              className="btn btn-outline-secondary retro-profile-btn"
            >
              <i className="bi bi-stars"></i> Try Retro Profile
            </a>
          </div>
        </div>

        <div className="profile-content">
          <div className="profile-section">
            <h2>Basic Information</h2>

            <div className="form-group">
              <label htmlFor="display_name">Display Name</label>
              <input
                type="text"
                id="display_name"
                name="display_name"
                value={userData.display_name}
                onChange={handleChange}
                placeholder="Enter your display name"
                className="form-control"
              />
            </div>

            <div className="form-group">
              <label htmlFor="bio">Bio</label>
              <textarea
                id="bio"
                name="bio"
                value={userData.bio}
                onChange={handleChange}
                placeholder="Tell us about yourself"
                className="form-control"
                rows="4"
              />
            </div>

            <div className="form-group">
              <label htmlFor="avatar">Profile Picture</label>
              <div className="avatar-upload-container">
                <div className="avatar-preview">
                  <img
                    src={avatarPreview || userData.avatar_url || (userData.is_premium ? '/default-avatar-crown.png' : '/default-avatar-specs.png')}
                    alt="Avatar preview"
                  />
                </div>
                <div className="avatar-upload-controls">
                  <input
                    type="file"
                    id="avatar"
                    name="avatar"
                    accept="image/*"
                    onChange={handleAvatarChange}
                    className="form-control"
                    style={{ display: 'none' }}
                  />
                  <label htmlFor="avatar" className="btn btn-outline-primary">
                    <i className="bi bi-upload"></i> Upload New Picture
                  </label>
                  <p className="avatar-help-text">Max size: 2MB. Recommended: square image (1:1 ratio)</p>
                </div>
              </div>
            </div>
          </div>

          <div className="profile-section">
            <h2>Certifications</h2>
            <p className="section-description">
              Certifications are earned through completing courses and assessments on Royaltea.
            </p>

            {userData.certifications && userData.certifications.length > 0 ? (
              <div className="certifications-container">
                {userData.certifications.map((cert, index) => (
                  <div key={index} className="certification-card">
                    <div className="certification-icon">
                      <i className={`bi ${cert.icon || 'bi-award'}`}></i>
                    </div>
                    <div className="certification-details">
                      <h3 className="certification-title">{cert.name}</h3>
                      <p className="certification-date">Earned on: {new Date(cert.earned_at).toLocaleDateString()}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="empty-state">
                <i className="bi bi-mortarboard-fill empty-icon"></i>
                <p>You haven't earned any certifications yet.</p>
                <a href="/learn" className="btn btn-outline-primary btn-sm">
                  Start Learning
                </a>
              </div>
            )}
          </div>

          <div className="profile-section">
            <h2>Awards & Achievements</h2>
            <p className="section-description">
              Awards are earned by reaching milestones and accomplishments on Royaltea.
            </p>

            {userData.awards && userData.awards.length > 0 ? (
              <div className="awards-container">
                {userData.awards.map((award, index) => (
                  <div key={index} className="award-card">
                    <div className="award-icon">
                      <i className={`bi ${award.icon || 'bi-trophy'}`}></i>
                    </div>
                    <div className="award-details">
                      <h3 className="award-title">{award.name}</h3>
                      <p className="award-description">{award.description}</p>
                      <p className="award-date">Earned on: {new Date(award.earned_at).toLocaleDateString()}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="empty-state">
                <i className="bi bi-trophy-fill empty-icon"></i>
                <p>You haven't earned any awards yet.</p>
                <p className="empty-state-hint">Complete projects and track your contributions to earn awards.</p>
              </div>
            )}
          </div>

          <div className="profile-section">
            <h2>Social Links</h2>

            <div className="form-group">
              <label htmlFor="social_links.github">GitHub</label>
              <input
                type="text"
                id="social_links.github"
                name="social_links.github"
                value={userData.social_links.github}
                onChange={handleChange}
                placeholder="Your GitHub profile URL"
                className="form-control"
              />
            </div>

            <div className="form-group">
              <label htmlFor="social_links.twitter">Twitter</label>
              <input
                type="text"
                id="social_links.twitter"
                name="social_links.twitter"
                value={userData.social_links.twitter}
                onChange={handleChange}
                placeholder="Your Twitter profile URL"
                className="form-control"
              />
            </div>

            <div className="form-group">
              <label htmlFor="social_links.linkedin">LinkedIn</label>
              <input
                type="text"
                id="social_links.linkedin"
                name="social_links.linkedin"
                value={userData.social_links.linkedin}
                onChange={handleChange}
                placeholder="Your LinkedIn profile URL"
                className="form-control"
              />
            </div>

            <div className="form-group">
              <label htmlFor="social_links.website">Personal Website</label>
              <input
                type="text"
                id="social_links.website"
                name="social_links.website"
                value={userData.social_links.website}
                onChange={handleChange}
                placeholder="Your personal website URL"
                className="form-control"
              />
            </div>
          </div>

          <div className="profile-section">
            <h2>Skills</h2>
            <p className="section-description">
              Showcase your skills and expertise. Add verifications to increase your credibility.
            </p>
            <UserSkillsList
              userId={currentUser?.id}
              isOwnProfile={true}
              onAddSkill={handleAddSkill}
            />
          </div>

          <div className="profile-section">
            <h2>Teams</h2>
            <p className="section-description">
              Teams allow you to collaborate with others on projects and manage ownership collectively.
            </p>
            <TeamProfileSection userId={currentUser?.id} />
          </div>

          <div className="profile-section">
            <h2>Preferences</h2>

            <div className="form-group">
              <label htmlFor="preferences.theme">Theme</label>
              <select
                id="preferences.theme"
                name="preferences.theme"
                value={userData.preferences.theme}
                onChange={handleChange}
                className="form-control"
              >
                <option value="light">Light</option>
                <option value="dark">Dark</option>
                <option value="system">System Default</option>
              </select>
            </div>

            <div className="form-check">
              <input
                type="checkbox"
                id="preferences.notifications_enabled"
                name="preferences.notifications_enabled"
                checked={userData.preferences.notifications_enabled}
                onChange={handleCheckboxChange}
                className="form-check-input"
              />
              <label
                htmlFor="preferences.notifications_enabled"
                className="form-check-label"
              >
                Enable Notifications
              </label>
            </div>
          </div>

          <div className="profile-actions">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={() => window.history.back()}
            >
              Cancel
            </button>
            <button
              type="button"
              className="btn btn-primary"
              onClick={handleSave}
              disabled={saving}
            >
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </div>

      {/* Skill Form Modal */}
      <SkillForm
        show={showSkillForm}
        onHide={() => setShowSkillForm(false)}
        userId={currentUser?.id}
        userSkill={selectedUserSkill}
        onSave={handleSkillSaved}
      />

      {/* Verification Form Modal */}
      {selectedUserSkill && (
        <VerificationForm
          show={showVerificationForm}
          onHide={() => setShowVerificationForm(false)}
          userSkill={selectedUserSkill}
          onSave={handleVerificationSaved}
        />
      )}
    </div>
  );
};

export default ProfilePage;
