// Quick React Test
const { test, expect } = require('@playwright/test');

test.describe('Quick React Test', () => {
  test('should check if React app is working', async ({ page }) => {
    console.log('🔍 Testing React app at http://localhost:5173');
    
    await page.goto('http://localhost:5173');
    
    // Wait a bit for React to initialize
    await page.waitForTimeout(3000);
    
    // Check page title
    const title = await page.title();
    console.log('Page title:', title);
    
    // Check if React has rendered content
    const bodyText = await page.textContent('body');
    console.log('Body text length:', bodyText.length);
    console.log('Body text preview:', bodyText.substring(0, 200));
    
    // Check for React elements
    const reactElements = await page.$$('[data-reactroot], [data-react-helmet], .react-component');
    console.log('React elements found:', reactElements.length);
    
    // Check if root element has content
    const rootElement = await page.$('#root');
    if (rootElement) {
      const rootContent = await rootElement.innerHTML();
      console.log('Root element has content:', rootContent.length > 50);
      console.log('Root content preview:', rootContent.substring(0, 200));
    } else {
      console.log('No root element found');
    }
    
    // Check for any JavaScript errors
    const jsErrors = [];
    page.on('pageerror', error => {
      jsErrors.push(error.message);
    });
    
    await page.waitForTimeout(2000);
    
    console.log('JavaScript errors:', jsErrors.length);
    if (jsErrors.length > 0) {
      console.log('Errors:', jsErrors);
    }
    
    // Basic assertion - page should load
    expect(title).toBeTruthy();
    expect(bodyText.length).toBeGreaterThan(10);
  });
});
