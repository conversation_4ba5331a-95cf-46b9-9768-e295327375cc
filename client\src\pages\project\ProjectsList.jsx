import React, { useState, useEffect, useContext, useRef, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from '../../utils/supabase/supabase.utils';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';
import SimpleLoading from '../../components/layout/SimpleLoading';
import { debounce } from 'lodash';

const ProjectsList = () => {
  const { currentUser } = useContext(UserContext);
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all'); // 'all', 'my', 'public'
  const [searchQuery, setSearchQuery] = useState('');
  const [projectTypes, setProjectTypes] = useState([]);
  const [selectedProjectType, setSelectedProjectType] = useState('all');
  const [sortBy, setSortBy] = useState('newest'); // 'newest', 'oldest', 'name_asc', 'name_desc'
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  // Refs for debouncing search
  const searchTimeoutRef = useRef(null);

  // Fetch project types for filter
  useEffect(() => {
    const fetchProjectTypes = async () => {
      if (!currentUser) return;

      try {
        const { data, error } = await supabase
          .from('projects')
          .select('project_type')
          .not('project_type', 'is', null);

        if (error) throw error;

        // Extract unique project types
        const types = [...new Set(data.map(p => p.project_type).filter(Boolean))];
        setProjectTypes(types);
      } catch (error) {
        console.error('Error fetching project types:', error);
      }
    };

    fetchProjectTypes();
  }, [currentUser]);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((query) => {
      fetchProjects();
    }, 500),
    [filter, selectedProjectType, sortBy]
  );

  // Handle search input change
  const handleSearchChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    debouncedSearch(query);
  };

  // Fetch projects
  useEffect(() => {
    const fetchProjects = async () => {
      if (!currentUser) return;

      try {
        setLoading(true);

        // First, get the total count for all projects (for pagination later)
        const countQuery = supabase.from('projects').select('id', { count: 'exact' });

        // Build the main query
        let query = supabase.from('projects').select('*');

        // Apply visibility filters
        if (filter === 'my') {
          // Fetch projects where user is a contributor
          const { data: contributorProjects, error: contributorError } = await supabase
            .from('project_contributors')
            .select('project_id')
            .eq('user_id', currentUser.id);

          if (contributorError) throw contributorError;

          if (contributorProjects && contributorProjects.length > 0) {
            const projectIds = contributorProjects.map(p => p.project_id);
            query = query.in('id', projectIds);
            countQuery.in('id', projectIds);
          } else {
            // No projects found
            setProjects([]);
            setTotalCount(0);
            setLoading(false);
            return;
          }
        } else if (filter === 'public') {
          // Fetch only public projects
          query = query.eq('is_public', true);
          countQuery.eq('is_public', true);
        }

        // Apply project type filter
        if (selectedProjectType !== 'all') {
          query = query.eq('project_type', selectedProjectType);
          countQuery.eq('project_type', selectedProjectType);
        }

        // Apply search query if present
        if (searchQuery && searchQuery.trim() !== '') {
          const searchTerm = `%${searchQuery.trim()}%`;
          query = query.or(`name.ilike.${searchTerm},description.ilike.${searchTerm}`);
          countQuery.or(`name.ilike.${searchTerm},description.ilike.${searchTerm}`);
        }

        // Apply sorting
        switch (sortBy) {
          case 'oldest':
            query = query.order('created_at', { ascending: true });
            break;
          case 'name_asc':
            query = query.order('name', { ascending: true });
            break;
          case 'name_desc':
            query = query.order('name', { ascending: false });
            break;
          case 'newest':
          default:
            query = query.order('created_at', { ascending: false });
            break;
        }

        // Get the count first
        const { count, error: countError } = await countQuery;

        if (countError) throw countError;

        setTotalCount(count || 0);

        // Execute the main query
        const { data, error } = await query;

        if (error) throw error;

        setProjects(data || []);
      } catch (error) {
        console.error('Error fetching projects:', error);
        toast.error('Failed to load projects');
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, [currentUser, filter, searchQuery, selectedProjectType, sortBy]);

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return <SimpleLoading text="Loading projects..." fullPage={true} />;
  }

  return (
    <div className="projects-list-container">
      <div className="projects-header">
        <h1>Projects</h1>
        <Link to="/project/wizard" className="btn btn-primary">
          <i className="bi bi-plus-lg"></i> New Project
        </Link>
      </div>

      <div className="projects-search-bar">
        <div className="search-input-container">
          <i className="bi bi-search search-icon"></i>
          <input
            type="text"
            placeholder="Search projects..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="search-input"
          />
          {searchQuery && (
            <button
              className="clear-search-button"
              onClick={() => {
                setSearchQuery('');
                debouncedSearch('');
              }}
              aria-label="Clear search"
            >
              <i className="bi bi-x-circle"></i>
            </button>
          )}
        </div>

        <button
          className="filter-toggle-button"
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
        >
          <i className="bi bi-sliders"></i>
          {showAdvancedFilters ? 'Hide Filters' : 'Show Filters'}
        </button>
      </div>

      <div className="projects-filters">
        <div className="visibility-filters">
          <button
            className={`filter-button ${filter === 'all' ? 'active' : ''}`}
            onClick={() => setFilter('all')}
          >
            All Projects
          </button>
          <button
            className={`filter-button ${filter === 'my' ? 'active' : ''}`}
            onClick={() => setFilter('my')}
          >
            My Projects
          </button>
          <button
            className={`filter-button ${filter === 'public' ? 'active' : ''}`}
            onClick={() => setFilter('public')}
          >
            Public Projects
          </button>
        </div>

        {showAdvancedFilters && (
          <div className="advanced-filters">
            <div className="filter-group">
              <label htmlFor="project-type-filter">Project Type:</label>
              <select
                id="project-type-filter"
                value={selectedProjectType}
                onChange={(e) => setSelectedProjectType(e.target.value)}
                className="filter-select"
              >
                <option value="all">All Types</option>
                {projectTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            <div className="filter-group">
              <label htmlFor="sort-by-filter">Sort By:</label>
              <select
                id="sort-by-filter"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="filter-select"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="name_asc">Name (A-Z)</option>
                <option value="name_desc">Name (Z-A)</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {searchQuery && (
        <div className="search-results-info">
          Found {totalCount} {totalCount === 1 ? 'project' : 'projects'} matching "{searchQuery}"
          <button
            className="clear-search-button-text"
            onClick={() => {
              setSearchQuery('');
              debouncedSearch('');
            }}
          >
            Clear Search
          </button>
        </div>
      )}

      {projects.length === 0 ? (
        <div className="no-projects">
          <p>No projects found.</p>
          {filter === 'my' && (
            <p>
              <Link to="/project/wizard" className="btn btn-outline-primary">
                Create your first project
              </Link>
            </p>
          )}
        </div>
      ) : (
        <div className="projects-grid">
          {projects.map(project => (
            <Link
              to={`/project/${project.id}`}
              key={project.id}
              className="project-card"
            >
              <div className="project-thumbnail">
                {project.thumbnail_url ? (
                  <img src={project.thumbnail_url} alt={project.name} />
                ) : (
                  <div className="placeholder-thumbnail">
                    <i className="bi bi-briefcase"></i>
                  </div>
                )}
              </div>
              <div className="project-info">
                <h3 className="project-name">{project.name}</h3>
                <div className="project-meta">
                  <span className="project-type">{project.project_type}</span>
                  {project.is_public && (
                    <span className="project-visibility">Public</span>
                  )}
                </div>
                <p className="project-description">
                  {project.description ? (
                    project.description.length > 100
                      ? `${project.description.substring(0, 100)}...`
                      : project.description
                  ) : (
                    'No description provided.'
                  )}
                </p>
                <div className="project-footer">
                  <span className="project-date">
                    Created: {formatDate(project.created_at)}
                  </span>
                </div>
              </div>
            </Link>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProjectsList;
