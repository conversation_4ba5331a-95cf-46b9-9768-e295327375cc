// Tailwind Plugin Import Debug Test
const { test, expect } = require('@playwright/test');

test.describe('Tailwind Plugin Import Debug', () => {
  test('should capture detailed stack trace for tailwindcss/plugin import error', async ({ page }) => {
    const errors = [];
    const consoleMessages = [];
    
    // Capture console messages
    page.on('console', msg => {
      consoleMessages.push({
        type: msg.type(),
        text: msg.text(),
        location: msg.location()
      });
    });
    
    // Capture JavaScript errors with detailed stack traces
    page.on('pageerror', error => {
      errors.push({
        name: error.name,
        message: error.message,
        stack: error.stack,
        fileName: error.fileName,
        lineNumber: error.lineNumber,
        columnNumber: error.columnNumber
      });
    });
    
    console.log('🔍 Loading page to debug tailwindcss/plugin import...');
    
    await page.goto('http://localhost:5173');
    
    // Wait for potential async errors
    await page.waitForTimeout(5000);
    
    console.log('\n=== TAILWIND PLUGIN IMPORT ANALYSIS ===');
    
    // Find the specific tailwindcss/plugin error
    const tailwindError = errors.find(error => 
      error.message && error.message.includes('tailwindcss/plugin')
    );
    
    if (tailwindError) {
      console.log('✅ Found tailwindcss/plugin error:');
      console.log('Message:', tailwindError.message);
      console.log('File:', tailwindError.fileName);
      console.log('Line:', tailwindError.lineNumber);
      console.log('Column:', tailwindError.columnNumber);
      console.log('Full Stack Trace:');
      console.log(tailwindError.stack);
      
      // Try to extract the module that's causing the import
      const stackLines = tailwindError.stack.split('\n');
      console.log('\n=== STACK TRACE ANALYSIS ===');
      stackLines.forEach((line, index) => {
        if (line.includes('http://localhost') || line.includes('node_modules')) {
          console.log(`${index + 1}. ${line.trim()}`);
        }
      });
    } else {
      console.log('❌ No tailwindcss/plugin error found in this session');
    }
    
    console.log('\n=== ALL ERRORS ===');
    errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.name}: ${error.message}`);
      if (error.fileName) {
        console.log(`   File: ${error.fileName}:${error.lineNumber}:${error.columnNumber}`);
      }
    });
    
    console.log('\n=== CONSOLE MESSAGES RELATED TO TAILWIND ===');
    const tailwindMessages = consoleMessages.filter(msg => 
      msg.text.toLowerCase().includes('tailwind') || 
      msg.text.includes('plugin') ||
      msg.text.includes('import')
    );
    
    if (tailwindMessages.length > 0) {
      tailwindMessages.forEach((msg, index) => {
        console.log(`${index + 1}. [${msg.type.toUpperCase()}] ${msg.text}`);
        if (msg.location) {
          console.log(`   Location: ${msg.location.url}:${msg.location.lineNumber}`);
        }
      });
    } else {
      console.log('No tailwind-related console messages found');
    }
    
    // Check what modules are actually loaded
    const loadedModules = await page.evaluate(() => {
      const scripts = Array.from(document.querySelectorAll('script[src]'));
      return scripts.map(script => script.src).filter(src => 
        src.includes('tailwind') || src.includes('plugin')
      );
    });
    
    console.log('\n=== LOADED MODULES (TAILWIND/PLUGIN) ===');
    if (loadedModules.length > 0) {
      loadedModules.forEach((module, index) => {
        console.log(`${index + 1}. ${module}`);
      });
    } else {
      console.log('No tailwind/plugin modules found in loaded scripts');
    }
    
    expect(true).toBe(true); // Always pass, we just want the debug info
  });
});
