// Direct React Test (no web server setup)
const { test, expect } = require('@playwright/test');

test.describe('Direct React Test', () => {
  test('should check React app directly', async ({ page }) => {
    console.log('🔍 Testing React app directly at http://localhost:5173');
    
    try {
      await page.goto('http://localhost:5173', { waitUntil: 'networkidle' });
      
      // Wait for React to initialize
      await page.waitForTimeout(5000);
      
      // Check page title
      const title = await page.title();
      console.log('✅ Page title:', title);
      
      // Check if root element exists and has content
      const rootElement = await page.$('#root');
      if (rootElement) {
        const rootContent = await rootElement.innerHTML();
        console.log('✅ Root element found with content length:', rootContent.length);
        
        if (rootContent.length > 100) {
          console.log('🎉 SUCCESS: React app is rendering content!');
          console.log('Root content preview:', rootContent.substring(0, 300));
        } else {
          console.log('⚠️ Root element exists but has minimal content');
          console.log('Root content:', rootContent);
        }
      } else {
        console.log('❌ No root element found');
      }
      
      // Check for React in window
      const hasReact = await page.evaluate(() => {
        return typeof window.React !== 'undefined';
      });
      console.log('React in window:', hasReact);
      
      // Check for any visible elements
      const visibleElements = await page.$$('body *:visible');
      console.log('Visible elements count:', visibleElements.length);
      
      // Check body text
      const bodyText = await page.textContent('body');
      console.log('Body text length:', bodyText.length);
      
      if (bodyText.length > 50) {
        console.log('✅ Body has substantial content');
      } else {
        console.log('⚠️ Body has minimal content:', bodyText);
      }
      
      expect(title).toBeTruthy();
      
    } catch (error) {
      console.log('❌ Error testing React app:', error.message);
      throw error;
    }
  });
});
