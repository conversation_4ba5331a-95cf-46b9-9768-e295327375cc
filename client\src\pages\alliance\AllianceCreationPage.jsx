import React, { useState, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardBody, Button } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import AllianceCreationWizard from '../../components/alliance/AllianceCreationWizard';

/**
 * Alliance Creation Page - Standalone page wrapper for alliance creation
 * 
 * This page provides a full-screen alliance creation experience that wraps
 * the AllianceCreationWizard modal component in a standalone page layout.
 */
const AllianceCreationPage = () => {
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();
  const [showWizard, setShowWizard] = useState(true);

  // Handle successful alliance creation
  const handleSuccess = (alliance) => {
    setShowWizard(false);
    // Navigate to the newly created alliance
    navigate(`/alliances/${alliance.id}`);
  };

  // Handle wizard close/cancel
  const handleClose = () => {
    setShowWizard(false);
    // Navigate back to teams page
    navigate('/teams');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-500/5 via-blue-500/5 to-slate-900">
      {/* Page Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white py-8">
        <div className="container mx-auto px-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="text-5xl">🏰</span>
              <div>
                <h1 className="text-4xl font-bold mb-2">Create New Alliance</h1>
                <p className="text-purple-100 text-lg">
                  Build your professional network and start collaborating
                </p>
              </div>
            </div>
            <Button
              variant="bordered"
              className="text-white border-white hover:bg-white/10"
              onClick={() => navigate('/teams')}
            >
              ← Back to Teams
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-12">
        {!showWizard ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <Card className="max-w-md mx-auto bg-white/10 backdrop-blur-md">
              <CardBody className="p-8">
                <div className="text-6xl mb-4">✅</div>
                <h2 className="text-2xl font-bold text-white mb-4">Alliance Created!</h2>
                <p className="text-white/70 mb-6">
                  Your alliance has been successfully created. You'll be redirected shortly.
                </p>
                <Button
                  color="primary"
                  onClick={() => navigate('/teams')}
                >
                  View My Alliances
                </Button>
              </CardBody>
            </Card>
          </motion.div>
        ) : (
          <div className="max-w-4xl mx-auto">
            <AllianceCreationWizard
              isOpen={showWizard}
              onClose={handleClose}
              onSuccess={handleSuccess}
              currentUser={currentUser}
            />
          </div>
        )}
      </div>

      {/* Info Section */}
      <div className="bg-white/5 backdrop-blur-md py-12 mt-12">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-4xl mb-4">🤝</div>
              <h3 className="text-xl font-semibold text-white mb-2">Collaborate</h3>
              <p className="text-white/70">
                Work with talented professionals on exciting projects
              </p>
            </div>
            <div>
              <div className="text-4xl mb-4">💰</div>
              <h3 className="text-xl font-semibold text-white mb-2">Earn Together</h3>
              <p className="text-white/70">
                Share revenue fairly based on contributions and agreements
              </p>
            </div>
            <div>
              <div className="text-4xl mb-4">🎯</div>
              <h3 className="text-xl font-semibold text-white mb-2">Achieve Goals</h3>
              <p className="text-white/70">
                Turn ideas into successful ventures with clear objectives
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AllianceCreationPage;
