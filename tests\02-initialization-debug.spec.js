// React Initialization Debug Tests
const { test, expect } = require('@playwright/test');

test.describe('React Initialization Debug', () => {
  test('should capture all JavaScript errors during page load', async ({ page }) => {
    const allErrors = [];
    const consoleMessages = [];
    
    // Capture all console messages
    page.on('console', msg => {
      consoleMessages.push({
        type: msg.type(),
        text: msg.text(),
        location: msg.location()
      });
    });

    // Capture all JavaScript errors
    page.on('pageerror', error => {
      allErrors.push({
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    });

    // Capture unhandled promise rejections
    page.on('requestfailed', request => {
      if (request.url().includes('.js')) {
        allErrors.push({
          type: 'request_failed',
          url: request.url(),
          error: request.failure()?.errorText
        });
      }
    });

    console.log('🔍 Loading page and capturing all errors...');
    
    await page.goto('/');
    
    // Wait longer to catch async errors
    await page.waitForTimeout(5000);
    
    console.log('=== ALL CONSOLE MESSAGES ===');
    consoleMessages.forEach((msg, index) => {
      console.log(`${index + 1}. [${msg.type.toUpperCase()}] ${msg.text}`);
      if (msg.location) {
        console.log(`   Location: ${msg.location.url}:${msg.location.lineNumber}`);
      }
    });

    console.log('=== ALL JAVASCRIPT ERRORS ===');
    if (allErrors.length === 0) {
      console.log('No JavaScript errors found');
    } else {
      allErrors.forEach((error, index) => {
        console.log(`${index + 1}. ${error.name || error.type}: ${error.message || error.error}`);
        if (error.stack) {
          console.log(`   Stack: ${error.stack.substring(0, 200)}...`);
        }
        if (error.url) {
          console.log(`   URL: ${error.url}`);
        }
      });
    }

    // Check environment variables in browser
    const envCheck = await page.evaluate(() => {
      return {
        hasSupabaseUrl: !!import.meta.env?.VITE_SUPABASE_URL,
        hasSupabaseKey: !!import.meta.env?.VITE_SUPABASE_ANON_KEY,
        nodeEnv: import.meta.env?.NODE_ENV,
        mode: import.meta.env?.MODE,
        allEnvKeys: Object.keys(import.meta.env || {})
      };
    });

    console.log('=== ENVIRONMENT VARIABLES ===');
    console.log('Has Supabase URL:', envCheck.hasSupabaseUrl);
    console.log('Has Supabase Key:', envCheck.hasSupabaseKey);
    console.log('Node Environment:', envCheck.nodeEnv);
    console.log('Mode:', envCheck.mode);
    console.log('Available env keys:', envCheck.allEnvKeys);

    // Check if main.jsx is executing
    const mainJsxCheck = await page.evaluate(() => {
      // Check if React root is being created
      const rootElement = document.getElementById('root');
      return {
        hasRootElement: !!rootElement,
        rootInnerHTML: rootElement?.innerHTML || 'No root element',
        rootChildrenCount: rootElement?.children?.length || 0,
        documentReadyState: document.readyState,
        hasReactInWindow: typeof window.React !== 'undefined',
        hasReactDOMInWindow: typeof window.ReactDOM !== 'undefined'
      };
    });

    console.log('=== REACT INITIALIZATION CHECK ===');
    console.log('Has root element:', mainJsxCheck.hasRootElement);
    console.log('Root innerHTML length:', mainJsxCheck.rootInnerHTML.length);
    console.log('Root children count:', mainJsxCheck.rootChildrenCount);
    console.log('Document ready state:', mainJsxCheck.documentReadyState);
    console.log('React in window:', mainJsxCheck.hasReactInWindow);
    console.log('ReactDOM in window:', mainJsxCheck.hasReactDOMInWindow);

    expect(true).toBe(true);
  });

  test('should test manual React initialization', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Try to manually initialize React to see what happens
    const manualInitResult = await page.evaluate(async () => {
      try {
        // Check if modules are available
        const hasReact = typeof React !== 'undefined';
        const hasReactDOM = typeof ReactDOM !== 'undefined';
        
        if (!hasReact || !hasReactDOM) {
          return {
            success: false,
            error: `Missing dependencies - React: ${hasReact}, ReactDOM: ${hasReactDOM}`
          };
        }

        // Try to create a simple React element
        const element = React.createElement('div', null, 'Test React Element');
        const root = document.getElementById('root');
        
        if (!root) {
          return {
            success: false,
            error: 'No root element found'
          };
        }

        // Try to render
        const reactRoot = ReactDOM.createRoot(root);
        reactRoot.render(element);
        
        return {
          success: true,
          message: 'Manual React initialization successful'
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          stack: error.stack
        };
      }
    });

    console.log('=== MANUAL REACT INITIALIZATION ===');
    console.log('Success:', manualInitResult.success);
    console.log('Message/Error:', manualInitResult.error || manualInitResult.message);
    if (manualInitResult.stack) {
      console.log('Stack:', manualInitResult.stack.substring(0, 300));
    }

    expect(true).toBe(true);
  });
});
