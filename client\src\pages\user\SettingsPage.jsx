import React, { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { ThemeContext } from '../../contexts/theme.context';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import LoadingAnimation from '../../components/layout/LoadingAnimation';
import AccessibilitySettings from '../../components/settings/AccessibilitySettings';

const SettingsPage = () => {
  const { currentUser } = useContext(UserContext);
  const { theme, setThemePreference, soundEnabled, setSoundPreference } = useContext(ThemeContext);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState({
    notifications: {
      email: true,
      app: true
    },
    privacy: {
      showEmail: false,
      showProjects: true
    },
    preferences: {
      theme: 'light',
      language: 'en',
      soundEnabled: true
    }
  });

  // Initialize theme and sound preferences from context
  useEffect(() => {
    setSettings(prevSettings => ({
      ...prevSettings,
      preferences: {
        ...prevSettings.preferences,
        theme: localStorage.getItem('theme') || theme,
        soundEnabled: soundEnabled
      }
    }));
  }, [theme, soundEnabled]);

  useEffect(() => {
    const fetchUserSettings = async () => {
      if (!currentUser) return;

      try {
        setLoading(true);

        // Fetch user data from Supabase
        const { data, error } = await supabase
          .from('users')
          .select('preferences')
          .eq('id', currentUser.id)
          .single();

        if (error) throw error;

        // If user has preferences stored, use them
        if (data && data.preferences) {
          // Merge with current settings, but keep theme from context
          const currentTheme = settings.preferences.theme;
          setSettings({
            ...settings,
            ...data.preferences,
            preferences: {
              ...data.preferences?.preferences,
              theme: currentTheme // Keep theme from context
            }
          });
        }
      } catch (error) {
        console.error('Error fetching user settings:', error);
        toast.error('Failed to load settings');
      } finally {
        setLoading(false);
      }
    };

    fetchUserSettings();
  }, [currentUser]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    // Handle nested properties using the name attribute format: "category.setting"
    if (name.includes('.')) {
      const [category, setting] = name.split('.');
      setSettings({
        ...settings,
        [category]: {
          ...settings[category],
          [setting]: type === 'checkbox' ? checked : value
        }
      });
    } else {
      setSettings({
        ...settings,
        [name]: type === 'checkbox' ? checked : value
      });
    }
  };

  const handleSave = async () => {
    if (!currentUser) return;

    try {
      setSaving(true);

      // Update user preferences in Supabase
      const { error } = await supabase
        .from('users')
        .update({
          preferences: settings
        })
        .eq('id', currentUser.id);

      if (error) throw error;

      toast.success('Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <LoadingAnimation />;
  }

  return (
    <div className="container mt-4">
      <div className="row">
        <div className="col-md-3">
          <div className="card mb-4">
            <div className="card-header">
              Settings
            </div>
            <div className="list-group list-group-flush">
              <button onClick={() => document.getElementById('notifications').scrollIntoView({ behavior: 'smooth' })} className="list-group-item list-group-item-action">Notifications</button>
              <button onClick={() => document.getElementById('privacy').scrollIntoView({ behavior: 'smooth' })} className="list-group-item list-group-item-action">Privacy</button>
              <button onClick={() => document.getElementById('preferences').scrollIntoView({ behavior: 'smooth' })} className="list-group-item list-group-item-action">Preferences</button>
              <button onClick={() => document.getElementById('accessibility').scrollIntoView({ behavior: 'smooth' })} className="list-group-item list-group-item-action">Accessibility</button>
              <button onClick={() => document.getElementById('account').scrollIntoView({ behavior: 'smooth' })} className="list-group-item list-group-item-action">Account</button>
            </div>
          </div>
        </div>

        <div className="col-md-9">
          <div className="card">
            <div className="card-header d-flex justify-content-between align-items-center">
              <h5 className="mb-0">User Settings</h5>
              <button
                className="btn btn-primary btn-sm"
                onClick={handleSave}
                disabled={saving}
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
            <div className="card-body">
              {/* Notifications Section */}
              <section id="notifications" className="mb-4">
                <h5>Notifications</h5>
                <hr />

                <div className="form-check mb-2">
                  <input
                    type="checkbox"
                    className="form-check-input"
                    id="notifications.email"
                    name="notifications.email"
                    checked={settings.notifications.email}
                    onChange={handleChange}
                  />
                  <label className="form-check-label" htmlFor="notifications.email">
                    Email Notifications
                  </label>
                  <div className="form-text">Receive notifications via email</div>
                </div>

                <div className="form-check">
                  <input
                    type="checkbox"
                    className="form-check-input"
                    id="notifications.app"
                    name="notifications.app"
                    checked={settings.notifications.app}
                    onChange={handleChange}
                  />
                  <label className="form-check-label" htmlFor="notifications.app">
                    In-App Notifications
                  </label>
                  <div className="form-text">Receive notifications within the application</div>
                </div>
              </section>

              {/* Privacy Section */}
              <section id="privacy" className="mb-4">
                <h5>Privacy</h5>
                <hr />

                <div className="form-check mb-2">
                  <input
                    type="checkbox"
                    className="form-check-input"
                    id="privacy.showEmail"
                    name="privacy.showEmail"
                    checked={settings.privacy.showEmail}
                    onChange={handleChange}
                  />
                  <label className="form-check-label" htmlFor="privacy.showEmail">
                    Show Email Address
                  </label>
                  <div className="form-text">Allow other users to see your email address</div>
                </div>

                <div className="form-check">
                  <input
                    type="checkbox"
                    className="form-check-input"
                    id="privacy.showProjects"
                    name="privacy.showProjects"
                    checked={settings.privacy.showProjects}
                    onChange={handleChange}
                  />
                  <label className="form-check-label" htmlFor="privacy.showProjects">
                    Show Projects
                  </label>
                  <div className="form-text">Allow other users to see your projects</div>
                </div>
              </section>

              {/* Preferences Section */}
              <section id="preferences" className="mb-4">
                <h5>Preferences</h5>
                <hr />

                <div className="mb-3">
                  <label htmlFor="preferences.theme" className="form-label">Theme</label>
                  <select
                    className="form-select"
                    id="preferences.theme"
                    name="preferences.theme"
                    value={settings.preferences.theme}
                    onChange={(e) => {
                      // Update local state
                      handleChange(e);
                      // Update theme context
                      setThemePreference(e.target.value);
                    }}
                  >
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                    <option value="system">System Default</option>
                  </select>
                  <div className="form-text">
                    Current theme: <strong>{theme === 'light' ? 'Light' : 'Dark'}</strong>
                    {settings.preferences.theme === 'system' && ' (based on system preference)'}
                  </div>
                </div>

                <div className="mb-3">
                  <label htmlFor="preferences.language" className="form-label">Language</label>
                  <select
                    className="form-select"
                    id="preferences.language"
                    name="preferences.language"
                    value={settings.preferences.language}
                    onChange={handleChange}
                  >
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                    <option value="de">German</option>
                  </select>
                </div>
              </section>

              {/* Accessibility Section */}
              <section id="accessibility" className="mb-4">
                <h5>Accessibility</h5>
                <hr />
                <AccessibilitySettings />
              </section>

              {/* Account Section */}
              <section id="account">
                <h5>Account</h5>
                <hr />

                <div className="mb-3">
                  <p>
                    <strong>Email:</strong> {currentUser?.email}
                  </p>
                </div>

                <div className="d-flex gap-2">
                  <Link to="/profile" className="btn btn-outline-primary">
                    Edit Profile
                  </Link>
                  <Link to="/password-reset" className="btn btn-outline-secondary">
                    Reset Password
                  </Link>
                </div>
              </section>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
