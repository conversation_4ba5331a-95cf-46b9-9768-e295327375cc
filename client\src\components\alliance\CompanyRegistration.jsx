import React, { useState, useContext } from 'react';
import { toast } from 'react-toastify';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';

/**
 * CompanyRegistration component - Business entity registration for alliances
 * Maintains gamified theming while ensuring legal compliance
 */
const CompanyRegistration = ({ allianceId, onCompanyRegistered, onCancel }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);
  
  const [companyData, setCompanyData] = useState({
    legal_name: '',
    doing_business_as: '',
    tax_id: '',
    company_type: 'llc',
    incorporation_state: '',
    incorporation_country: 'US',
    incorporation_date: '',
    industry_classification: '',
    business_description: '',
    website_url: '',
    primary_address: {
      street: '',
      city: '',
      state: '',
      zip: '',
      country: 'US'
    },
    mailing_address: null,
    primary_email: '',
    primary_phone: '',
    fiscal_year_end: '',
    accounting_method: 'accrual'
  });

  const [errors, setErrors] = useState({});

  const validateStep = (stepNumber) => {
    const newErrors = {};
    
    if (stepNumber === 1) {
      if (!companyData.legal_name.trim()) newErrors.legal_name = 'Legal name is required';
      if (!companyData.tax_id.trim()) newErrors.tax_id = 'Tax ID is required';
      if (!companyData.company_type) newErrors.company_type = 'Company type is required';
      
      // Validate EIN format for US companies
      if (companyData.incorporation_country === 'US' && companyData.tax_id) {
        const einRegex = /^\d{2}-\d{7}$/;
        if (!einRegex.test(companyData.tax_id)) {
          newErrors.tax_id = 'US EIN must be in format XX-XXXXXXX';
        }
      }
    }
    
    if (stepNumber === 2) {
      if (!companyData.primary_address.street.trim()) newErrors.street = 'Street address is required';
      if (!companyData.primary_address.city.trim()) newErrors.city = 'City is required';
      if (!companyData.primary_address.state.trim()) newErrors.state = 'State is required';
      if (!companyData.primary_address.zip.trim()) newErrors.zip = 'ZIP code is required';
      if (!companyData.primary_email.trim()) newErrors.primary_email = 'Email is required';
      
      // Validate email format
      const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
      if (companyData.primary_email && !emailRegex.test(companyData.primary_email)) {
        newErrors.primary_email = 'Invalid email format';
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(step)) {
      setStep(step + 1);
    }
  };

  const handleBack = () => {
    setStep(step - 1);
    setErrors({});
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateStep(2)) return;
    
    try {
      setLoading(true);
      
      // Call the companies API
      const response = await fetch('/.netlify/functions/companies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        },
        body: JSON.stringify(companyData)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to register company');
      }
      
      const { company } = await response.json();
      
      // Link the company to the alliance
      const { error: linkError } = await supabase
        .from('teams')
        .update({
          company_id: company.id,
          is_business_entity: true,
          alliance_type: 'established',
          updated_at: new Date()
        })
        .eq('id', allianceId);
      
      if (linkError) throw linkError;
      
      toast.success('🏰 Business entity registered successfully! Alliance elevated to Established status.');
      onCompanyRegistered(company);
      
    } catch (error) {
      console.error('Error registering company:', error);
      toast.error(error.message || 'Failed to register business entity');
    } finally {
      setLoading(false);
    }
  };

  const updateCompanyData = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setCompanyData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setCompanyData(prev => ({
        ...prev,
        [field]: value
      }));
    }
    
    // Clear error when user starts typing
    if (errors[field] || errors[field.split('.')[1]]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        delete newErrors[field.split('.')[1]];
        return newErrors;
      });
    }
  };

  return (
    <div className="company-registration">
      <div className="registration-header">
        <h3>🏰 Register Business Entity</h3>
        <p>Elevate your alliance to Established status with legal business registration</p>
        <div className="step-indicator">
          <span className={step >= 1 ? 'active' : ''}>1. Business Info</span>
          <span className={step >= 2 ? 'active' : ''}>2. Contact Details</span>
          <span className={step >= 3 ? 'active' : ''}>3. Review</span>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="registration-form">
        {step === 1 && (
          <div className="step-content">
            <h4>📋 Business Information</h4>
            
            <div className="form-group">
              <label htmlFor="legal_name">Legal Business Name *</label>
              <input
                id="legal_name"
                type="text"
                value={companyData.legal_name}
                onChange={(e) => updateCompanyData('legal_name', e.target.value)}
                placeholder="e.g., VRC Entertainment LLC"
                className={errors.legal_name ? 'error' : ''}
              />
              {errors.legal_name && <span className="error-text">{errors.legal_name}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="doing_business_as">Doing Business As (DBA)</label>
              <input
                id="doing_business_as"
                type="text"
                value={companyData.doing_business_as}
                onChange={(e) => updateCompanyData('doing_business_as', e.target.value)}
                placeholder="e.g., VRC Films"
              />
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="tax_id">Tax ID (EIN) *</label>
                <input
                  id="tax_id"
                  type="text"
                  value={companyData.tax_id}
                  onChange={(e) => updateCompanyData('tax_id', e.target.value)}
                  placeholder="XX-XXXXXXX"
                  className={errors.tax_id ? 'error' : ''}
                />
                {errors.tax_id && <span className="error-text">{errors.tax_id}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="company_type">Business Type *</label>
                <select
                  id="company_type"
                  value={companyData.company_type}
                  onChange={(e) => updateCompanyData('company_type', e.target.value)}
                  className={errors.company_type ? 'error' : ''}
                >
                  <option value="llc">LLC</option>
                  <option value="corporation">Corporation</option>
                  <option value="partnership">Partnership</option>
                  <option value="sole_proprietorship">Sole Proprietorship</option>
                </select>
                {errors.company_type && <span className="error-text">{errors.company_type}</span>}
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="incorporation_state">State of Incorporation</label>
                <input
                  id="incorporation_state"
                  type="text"
                  value={companyData.incorporation_state}
                  onChange={(e) => updateCompanyData('incorporation_state', e.target.value)}
                  placeholder="e.g., CA"
                />
              </div>

              <div className="form-group">
                <label htmlFor="incorporation_date">Incorporation Date</label>
                <input
                  id="incorporation_date"
                  type="date"
                  value={companyData.incorporation_date}
                  onChange={(e) => updateCompanyData('incorporation_date', e.target.value)}
                />
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="business_description">Business Description</label>
              <textarea
                id="business_description"
                value={companyData.business_description}
                onChange={(e) => updateCompanyData('business_description', e.target.value)}
                placeholder="Describe your business activities"
                rows="3"
              />
            </div>
          </div>
        )}

        {step === 2 && (
          <div className="step-content">
            <h4>📍 Contact Information</h4>
            
            <div className="form-group">
              <label htmlFor="street">Business Address *</label>
              <input
                id="street"
                type="text"
                value={companyData.primary_address.street}
                onChange={(e) => updateCompanyData('primary_address.street', e.target.value)}
                placeholder="Street address"
                className={errors.street ? 'error' : ''}
              />
              {errors.street && <span className="error-text">{errors.street}</span>}
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="city">City *</label>
                <input
                  id="city"
                  type="text"
                  value={companyData.primary_address.city}
                  onChange={(e) => updateCompanyData('primary_address.city', e.target.value)}
                  placeholder="City"
                  className={errors.city ? 'error' : ''}
                />
                {errors.city && <span className="error-text">{errors.city}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="state">State *</label>
                <input
                  id="state"
                  type="text"
                  value={companyData.primary_address.state}
                  onChange={(e) => updateCompanyData('primary_address.state', e.target.value)}
                  placeholder="State"
                  className={errors.state ? 'error' : ''}
                />
                {errors.state && <span className="error-text">{errors.state}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="zip">ZIP Code *</label>
                <input
                  id="zip"
                  type="text"
                  value={companyData.primary_address.zip}
                  onChange={(e) => updateCompanyData('primary_address.zip', e.target.value)}
                  placeholder="ZIP"
                  className={errors.zip ? 'error' : ''}
                />
                {errors.zip && <span className="error-text">{errors.zip}</span>}
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="primary_email">Business Email *</label>
                <input
                  id="primary_email"
                  type="email"
                  value={companyData.primary_email}
                  onChange={(e) => updateCompanyData('primary_email', e.target.value)}
                  placeholder="<EMAIL>"
                  className={errors.primary_email ? 'error' : ''}
                />
                {errors.primary_email && <span className="error-text">{errors.primary_email}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="primary_phone">Business Phone</label>
                <input
                  id="primary_phone"
                  type="tel"
                  value={companyData.primary_phone}
                  onChange={(e) => updateCompanyData('primary_phone', e.target.value)}
                  placeholder="******-123-4567"
                />
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="website_url">Website</label>
              <input
                id="website_url"
                type="url"
                value={companyData.website_url}
                onChange={(e) => updateCompanyData('website_url', e.target.value)}
                placeholder="https://yourwebsite.com"
              />
            </div>
          </div>
        )}

        {step === 3 && (
          <div className="step-content">
            <h4>✅ Review & Confirm</h4>
            <div className="review-section">
              <div className="review-item">
                <strong>Legal Name:</strong> {companyData.legal_name}
              </div>
              <div className="review-item">
                <strong>Tax ID:</strong> {companyData.tax_id}
              </div>
              <div className="review-item">
                <strong>Business Type:</strong> {companyData.company_type.toUpperCase()}
              </div>
              <div className="review-item">
                <strong>Address:</strong> {companyData.primary_address.street}, {companyData.primary_address.city}, {companyData.primary_address.state} {companyData.primary_address.zip}
              </div>
              <div className="review-item">
                <strong>Email:</strong> {companyData.primary_email}
              </div>
            </div>
            
            <div className="compliance-notice">
              <h5>🛡️ Compliance Benefits</h5>
              <ul>
                <li>✅ Tax-compliant financial transactions</li>
                <li>✅ Automatic 1099 generation</li>
                <li>✅ Professional business reporting</li>
                <li>✅ Legal protection and liability coverage</li>
              </ul>
            </div>
          </div>
        )}

        <div className="form-actions">
          {step > 1 && (
            <button type="button" onClick={handleBack} className="back-button">
              Back
            </button>
          )}
          
          {step < 3 ? (
            <button type="button" onClick={handleNext} className="next-button">
              Next
            </button>
          ) : (
            <button type="submit" disabled={loading} className="submit-button">
              {loading ? 'Registering...' : '🏰 Register Business Entity'}
            </button>
          )}
          
          <button type="button" onClick={onCancel} className="cancel-button">
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};

export default CompanyRegistration;
