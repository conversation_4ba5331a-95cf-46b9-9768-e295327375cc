// Performance and Load Testing
const { test, expect } = require('@playwright/test');

test.describe('Performance Tests', () => {
  test('should load pages within acceptable time limits', async ({ page }) => {
    const pages = [
      '/',
      '/missions',
      '/teams',
      '/alliances',
      '/vetting',
      '/ventures',
      '/quests'
    ];
    
    for (const pagePath of pages) {
      const startTime = Date.now();
      
      await page.goto(pagePath);
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      
      console.log(`${pagePath}: ${loadTime}ms`);
      
      // Pages should load within 5 seconds
      expect(loadTime).toBeLessThan(5000);
      
      // Critical pages should load within 2 seconds
      if (pagePath === '/' || pagePath === '/missions') {
        expect(loadTime).toBeLessThan(2000);
      }
    }
  });

  test('should have acceptable Core Web Vitals', async ({ page }) => {
    await page.goto('/');
    
    // Measure Core Web Vitals
    const vitals = await page.evaluate(() => {
      return new Promise((resolve) => {
        const vitals = {};
        
        // Largest Contentful Paint (LCP)
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          vitals.lcp = lastEntry.startTime;
        }).observe({ entryTypes: ['largest-contentful-paint'] });
        
        // First Input Delay (FID) - simulated
        vitals.fid = 0; // Will be 0 in automated tests
        
        // Cumulative Layout Shift (CLS)
        let clsValue = 0;
        new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          }
          vitals.cls = clsValue;
        }).observe({ entryTypes: ['layout-shift'] });
        
        // Wait a bit for measurements
        setTimeout(() => resolve(vitals), 3000);
      });
    });
    
    console.log('Core Web Vitals:', vitals);
    
    // LCP should be under 2.5 seconds
    if (vitals.lcp) {
      expect(vitals.lcp).toBeLessThan(2500);
    }
    
    // CLS should be under 0.1
    if (vitals.cls !== undefined) {
      expect(vitals.cls).toBeLessThan(0.1);
    }
  });

  test('should handle multiple concurrent users', async ({ browser }) => {
    const contexts = [];
    const pages = [];
    
    // Simulate 5 concurrent users
    for (let i = 0; i < 5; i++) {
      const context = await browser.newContext();
      const page = await context.newPage();
      contexts.push(context);
      pages.push(page);
    }
    
    try {
      // All users navigate to different pages simultaneously
      const navigationPromises = pages.map((page, index) => {
        const routes = ['/', '/missions', '/teams', '/alliances', '/vetting'];
        return page.goto(routes[index % routes.length]);
      });
      
      const startTime = Date.now();
      await Promise.all(navigationPromises);
      
      // Wait for all pages to load
      await Promise.all(pages.map(page => page.waitForLoadState('networkidle')));
      
      const totalTime = Date.now() - startTime;
      console.log(`Concurrent load time: ${totalTime}ms`);
      
      // Should handle concurrent load within reasonable time
      expect(totalTime).toBeLessThan(10000);
      
      // All pages should be functional
      for (const page of pages) {
        await expect(page.locator('body')).toBeVisible();
      }
      
    } finally {
      // Clean up
      for (const context of contexts) {
        await context.close();
      }
    }
  });

  test('should have efficient bundle sizes', async ({ page }) => {
    const resourceSizes = [];
    
    page.on('response', response => {
      const url = response.url();
      const size = response.headers()['content-length'];
      
      if (url.includes('.js') || url.includes('.css')) {
        resourceSizes.push({
          url: url.split('/').pop(),
          size: parseInt(size) || 0,
          type: url.includes('.js') ? 'js' : 'css'
        });
      }
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    console.log('Resource sizes:', resourceSizes);
    
    // Check for reasonable bundle sizes
    const jsFiles = resourceSizes.filter(r => r.type === 'js');
    const cssFiles = resourceSizes.filter(r => r.type === 'css');
    
    const totalJsSize = jsFiles.reduce((sum, file) => sum + file.size, 0);
    const totalCssSize = cssFiles.reduce((sum, file) => sum + file.size, 0);
    
    console.log(`Total JS size: ${totalJsSize} bytes`);
    console.log(`Total CSS size: ${totalCssSize} bytes`);
    
    // Reasonable limits (these can be adjusted based on requirements)
    // Total JS should be under 2MB for initial load
    expect(totalJsSize).toBeLessThan(2 * 1024 * 1024);
    
    // Total CSS should be under 500KB
    expect(totalCssSize).toBeLessThan(500 * 1024);
  });

  test('should handle memory usage efficiently', async ({ page }) => {
    await page.goto('/');
    
    // Get initial memory usage
    const initialMemory = await page.evaluate(() => {
      return performance.memory ? {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      } : null;
    });
    
    if (initialMemory) {
      console.log('Initial memory usage:', initialMemory);
      
      // Navigate through several pages
      const pages = ['/missions', '/teams', '/alliances', '/vetting', '/ventures'];
      
      for (const pagePath of pages) {
        await page.goto(pagePath);
        await page.waitForLoadState('networkidle');
      }
      
      // Check memory after navigation
      const finalMemory = await page.evaluate(() => {
        return {
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit
        };
      });
      
      console.log('Final memory usage:', finalMemory);
      
      // Memory usage shouldn't grow excessively
      const memoryGrowth = finalMemory.used - initialMemory.used;
      console.log(`Memory growth: ${memoryGrowth} bytes`);
      
      // Memory growth should be reasonable (under 50MB)
      expect(memoryGrowth).toBeLessThan(50 * 1024 * 1024);
    }
  });
});
