// Detailed Debug Test
const { test, expect } = require('@playwright/test');

test.describe('Detailed Debug Test', () => {
  test('should capture all errors and console messages', async ({ page }) => {
    const errors = [];
    const consoleMessages = [];
    const networkErrors = [];
    
    // Capture console messages
    page.on('console', msg => {
      consoleMessages.push({
        type: msg.type(),
        text: msg.text(),
        location: msg.location()
      });
    });
    
    // Capture JavaScript errors
    page.on('pageerror', error => {
      errors.push({
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    });
    
    // Capture network failures
    page.on('requestfailed', request => {
      networkErrors.push({
        url: request.url(),
        method: request.method(),
        error: request.failure()?.errorText
      });
    });
    
    console.log('🔍 Loading page with detailed error capture...');
    
    try {
      await page.goto('http://localhost:5173', { 
        waitUntil: 'networkidle',
        timeout: 10000 
      });
      
      // Wait for potential async errors
      await page.waitForTimeout(5000);
      
      console.log('\n=== PAGE ANALYSIS ===');
      const title = await page.title();
      console.log('Page title:', title || '(empty)');
      
      const html = await page.content();
      console.log('HTML length:', html.length);
      console.log('HTML preview:', html.substring(0, 500));
      
      console.log('\n=== CONSOLE MESSAGES ===');
      if (consoleMessages.length === 0) {
        console.log('No console messages');
      } else {
        consoleMessages.forEach((msg, i) => {
          console.log(`${i + 1}. [${msg.type.toUpperCase()}] ${msg.text}`);
          if (msg.location) {
            console.log(`   Location: ${msg.location.url}:${msg.location.lineNumber}`);
          }
        });
      }
      
      console.log('\n=== JAVASCRIPT ERRORS ===');
      if (errors.length === 0) {
        console.log('No JavaScript errors');
      } else {
        errors.forEach((error, i) => {
          console.log(`${i + 1}. ${error.name}: ${error.message}`);
          if (error.stack) {
            console.log(`   Stack: ${error.stack.substring(0, 200)}...`);
          }
        });
      }
      
      console.log('\n=== NETWORK ERRORS ===');
      if (networkErrors.length === 0) {
        console.log('No network errors');
      } else {
        networkErrors.forEach((error, i) => {
          console.log(`${i + 1}. ${error.method} ${error.url}`);
          console.log(`   Error: ${error.error}`);
        });
      }
      
      console.log('\n=== DOM ANALYSIS ===');
      const rootElement = await page.$('#root');
      if (rootElement) {
        const rootHTML = await rootElement.innerHTML();
        console.log('Root element found, content length:', rootHTML.length);
        if (rootHTML.length > 0) {
          console.log('Root content preview:', rootHTML.substring(0, 300));
        }
      } else {
        console.log('❌ No root element found');
      }
      
      // Check if scripts are loading
      const scripts = await page.$$eval('script', scripts => 
        scripts.map(script => ({
          src: script.src,
          type: script.type,
          hasContent: script.innerHTML.length > 0
        }))
      );
      
      console.log('\n=== SCRIPTS ===');
      scripts.forEach((script, i) => {
        console.log(`${i + 1}. ${script.src || '(inline)'} - Type: ${script.type || 'text/javascript'} - Has content: ${script.hasContent}`);
      });
      
      // Check if main.jsx is accessible
      try {
        const mainJsResponse = await page.request.get('http://localhost:5173/src/main.jsx');
        console.log('\n=== MAIN.JSX ACCESS ===');
        console.log('Status:', mainJsResponse.status());
        console.log('Content-Type:', mainJsResponse.headers()['content-type']);
      } catch (error) {
        console.log('\n=== MAIN.JSX ACCESS ERROR ===');
        console.log('Error accessing main.jsx:', error.message);
      }
      
      expect(true).toBe(true); // Always pass, we just want the debug info
      
    } catch (error) {
      console.log('\n❌ CRITICAL ERROR:', error.message);
      throw error;
    }
  });
});
