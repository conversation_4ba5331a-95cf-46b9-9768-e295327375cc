import React, { useState, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { Navigate } from 'react-router-dom';
import BugReportForm from '../../components/bug/BugReportForm';
import KnownBugsList from '../../components/bugs/KnownBugsList';

const BugReportPage = () => {
  const { currentUser } = useContext(UserContext);
  const [showReportForm, setShowReportForm] = useState(false);
  const [reportSuccess, setReportSuccess] = useState(false);

  // Handle successful bug report submission
  const handleReportSuccess = () => {
    setShowReportForm(false);
    setReportSuccess(true);

    // Reset success message after 5 seconds
    setTimeout(() => {
      setReportSuccess(false);
    }, 5000);
  };

  // If not logged in, redirect to login
  if (!currentUser) {
    return <Navigate to="/login" />;
  }

  return (
    <div className="bug-report-page">
      <div className="bug-report-page-header">
        <h1>Bug Reports & Known Issues</h1>
        <p>Help us improve Royaltea by reporting bugs and checking known issues</p>

        {!showReportForm && (
          <button
            className="report-bug-button"
            onClick={() => setShowReportForm(true)}
          >
            <i className="bi bi-bug"></i> Report a Bug
          </button>
        )}
      </div>

      {reportSuccess && (
        <div className="success-message">
          <i className="bi bi-check-circle"></i>
          <p>Thank you for your bug report! Our team will review it soon.</p>
        </div>
      )}

      {showReportForm ? (
        <div className="bug-form-section">
          <BugReportForm
            onSuccess={handleReportSuccess}
            onCancel={() => setShowReportForm(false)}
          />
        </div>
      ) : (
        <div className="known-bugs-section">
          <KnownBugsList />
        </div>
      )}
    </div>
  );
};

export default BugReportPage;
