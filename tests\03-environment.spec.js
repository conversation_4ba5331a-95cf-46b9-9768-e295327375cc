// Environment and API Integration Tests
const { test, expect } = require('@playwright/test');

test.describe('Environment & API Integration', () => {
  test('should have environment variables configured', async ({ page }) => {
    await page.goto('/test/environment');
    await page.waitForLoadState('networkidle');
    
    // Check for environment test results
    const envStatus = page.locator('[data-testid="environment-status"]');
    if (await envStatus.count() > 0) {
      await expect(envStatus).toBeVisible();
      
      // Check that critical services are configured
      const supabaseStatus = page.locator('[data-testid="supabase-status"]');
      if (await supabaseStatus.count() > 0) {
        const statusText = await supabaseStatus.textContent();
        expect(statusText).toMatch(/(connected|configured|working)/i);
      }
    }
  });

  test('should handle API errors gracefully', async ({ page }) => {
    // Intercept API calls and return errors
    await page.route('**/.netlify/functions/**', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Test error' })
      });
    });
    
    await page.goto('/missions');
    await page.waitForLoadState('networkidle');
    
    // Page should still load and show error state
    await expect(page.locator('body')).toBeVisible();
    
    // Should show error message or fallback content
    const errorMessage = page.locator('text=Error').or(page.locator('text=Unable to load'));
    const fallbackContent = page.locator('text=No missions').or(page.locator('text=Try again'));
    
    await expect(errorMessage.or(fallbackContent)).toBeVisible();
  });

  test('should handle slow API responses', async ({ page }) => {
    // Simulate slow API responses
    await page.route('**/.netlify/functions/**', route => {
      setTimeout(() => route.continue(), 2000);
    });
    
    await page.goto('/missions');
    
    // Should show loading state
    const loadingState = page.locator('text=Loading').or(page.locator('[data-testid="loading"]'));
    
    // Wait for content to eventually load
    await page.waitForLoadState('networkidle', { timeout: 10000 });
    
    // Content should be visible after loading
    await expect(page.locator('body')).toBeVisible();
  });

  test('should work offline (basic functionality)', async ({ page, context }) => {
    // Go online first to load the page
    await page.goto('/missions');
    await page.waitForLoadState('networkidle');
    
    // Go offline
    await context.setOffline(true);
    
    // Try to navigate to another page
    await page.goto('/teams');
    
    // Should handle offline state gracefully
    await expect(page.locator('body')).toBeVisible();
    
    // Go back online
    await context.setOffline(false);
  });

  test('should have proper security headers', async ({ page }) => {
    const response = await page.goto('/');
    
    // Check for basic security headers
    const headers = response.headers();
    
    // These are good to have but not required for functionality
    console.log('Security headers check:');
    console.log('X-Frame-Options:', headers['x-frame-options'] || 'Not set');
    console.log('X-Content-Type-Options:', headers['x-content-type-options'] || 'Not set');
    console.log('Referrer-Policy:', headers['referrer-policy'] || 'Not set');
  });

  test('should load static assets correctly', async ({ page }) => {
    await page.goto('/');
    
    // Check for CSS loading
    const stylesheets = await page.locator('link[rel="stylesheet"]').count();
    expect(stylesheets).toBeGreaterThan(0);
    
    // Check for JavaScript loading
    const scripts = await page.locator('script[src]').count();
    expect(scripts).toBeGreaterThan(0);
    
    // Check that no critical resources failed to load
    const failedRequests = [];
    page.on('requestfailed', request => {
      failedRequests.push(request.url());
    });
    
    await page.waitForLoadState('networkidle');
    
    // Filter out non-critical failures (like analytics, ads, etc.)
    const criticalFailures = failedRequests.filter(url => 
      url.includes('.js') || url.includes('.css') || url.includes('api')
    );
    
    expect(criticalFailures.length).toBe(0);
  });

  test('should have working service worker (if implemented)', async ({ page }) => {
    await page.goto('/');
    
    // Check if service worker is registered
    const swRegistration = await page.evaluate(() => {
      return navigator.serviceWorker.getRegistration();
    });
    
    if (swRegistration) {
      console.log('✅ Service worker is registered');
      
      // Check if it's active
      const swActive = await page.evaluate(() => {
        return navigator.serviceWorker.controller !== null;
      });
      
      if (swActive) {
        console.log('✅ Service worker is active');
      }
    } else {
      console.log('ℹ️ No service worker registered (optional)');
    }
  });
});
