import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { supabase } from '../../utils/supabase/supabase.utils';

/**
 * AlliancePermissions component - Enhanced permission management for business alliances
 * Maintains gamified theming while adding business-level access controls
 */
const AlliancePermissions = ({ allianceId, isFounder, hasBusinessEntity, onPermissionsUpdated }) => {
  const [loading, setLoading] = useState(false);
  const [businessRoles, setBusinessRoles] = useState([]);
  const [members, setMembers] = useState([]);
  const [showCreateRole, setShowCreateRole] = useState(false);
  
  const [newRole, setNewRole] = useState({
    role_name: '',
    can_manage_finances: false,
    can_approve_payments: false,
    can_manage_alliances: false,
    can_manage_users: false,
    can_view_reports: false
  });

  useEffect(() => {
    if (hasBusinessEntity) {
      fetchBusinessRoles();
      fetchMembersWithRoles();
    }
  }, [allianceId, hasBusinessEntity]);

  const fetchBusinessRoles = async () => {
    try {
      // For now, we'll create default roles if they don't exist
      // In a real implementation, this would fetch from business_roles table
      const defaultRoles = [
        {
          id: 'founder',
          role_name: '👑 Alliance Founder',
          can_manage_finances: true,
          can_approve_payments: true,
          can_manage_alliances: true,
          can_manage_users: true,
          can_view_reports: true
        },
        {
          id: 'leader',
          role_name: '🛡️ Alliance Leader',
          can_manage_finances: true,
          can_approve_payments: true,
          can_manage_alliances: false,
          can_manage_users: true,
          can_view_reports: true
        },
        {
          id: 'treasurer',
          role_name: '💰 Alliance Treasurer',
          can_manage_finances: true,
          can_approve_payments: true,
          can_manage_alliances: false,
          can_manage_users: false,
          can_view_reports: true
        },
        {
          id: 'member',
          role_name: '⚔️ Alliance Member',
          can_manage_finances: false,
          can_approve_payments: false,
          can_manage_alliances: false,
          can_manage_users: false,
          can_view_reports: false
        }
      ];
      
      setBusinessRoles(defaultRoles);
    } catch (error) {
      console.error('Error fetching business roles:', error);
      toast.error('Failed to load business roles');
    }
  };

  const fetchMembersWithRoles = async () => {
    try {
      const { data: membersData, error } = await supabase
        .from('team_members')
        .select(`
          id,
          role,
          is_admin,
          user:user_id(id, email, user_metadata)
        `)
        .eq('team_id', allianceId);

      if (error) throw error;

      const processedMembers = membersData.map(member => ({
        id: member.id,
        userId: member.user.id,
        email: member.user.email,
        displayName: member.user.user_metadata?.full_name || member.user.email,
        role: member.role,
        isAdmin: member.is_admin,
        businessRole: member.role === 'owner' ? 'founder' : 
                     member.is_admin ? 'leader' : 'member'
      }));

      setMembers(processedMembers);
    } catch (error) {
      console.error('Error fetching members:', error);
      toast.error('Failed to load alliance members');
    }
  };

  const handleCreateRole = async (e) => {
    e.preventDefault();
    
    if (!newRole.role_name.trim()) {
      toast.error('Role name is required');
      return;
    }

    try {
      setLoading(true);
      
      // In a real implementation, this would create a role in business_roles table
      const roleId = `custom_${Date.now()}`;
      const customRole = {
        id: roleId,
        ...newRole
      };
      
      setBusinessRoles(prev => [...prev, customRole]);
      setShowCreateRole(false);
      setNewRole({
        role_name: '',
        can_manage_finances: false,
        can_approve_payments: false,
        can_manage_alliances: false,
        can_manage_users: false,
        can_view_reports: false
      });
      
      toast.success('Custom role created successfully');
    } catch (error) {
      console.error('Error creating role:', error);
      toast.error('Failed to create role');
    } finally {
      setLoading(false);
    }
  };

  const handleAssignRole = async (memberId, roleId) => {
    try {
      setLoading(true);
      
      // Update member's business role
      setMembers(prev => prev.map(member => 
        member.id === memberId 
          ? { ...member, businessRole: roleId }
          : member
      ));
      
      toast.success('Role assigned successfully');
      onPermissionsUpdated();
    } catch (error) {
      console.error('Error assigning role:', error);
      toast.error('Failed to assign role');
    } finally {
      setLoading(false);
    }
  };

  const getRolePermissions = (roleId) => {
    return businessRoles.find(role => role.id === roleId) || businessRoles.find(role => role.id === 'member');
  };

  const getPermissionIcon = (hasPermission) => {
    return hasPermission ? '✅' : '❌';
  };

  if (!hasBusinessEntity) {
    return (
      <div className="alliance-permissions">
        <h3>🛡️ Alliance Permissions</h3>
        <div className="no-business-entity">
          <p>🏰 Register as a business entity to unlock advanced permission management</p>
          <ul>
            <li>💰 Financial management roles</li>
            <li>📊 Reporting access controls</li>
            <li>🔐 Payment approval workflows</li>
            <li>👥 User management permissions</li>
          </ul>
        </div>
      </div>
    );
  }

  return (
    <div className="alliance-permissions">
      <div className="permissions-header">
        <h3>🛡️ Alliance Permissions</h3>
        {isFounder && (
          <button
            onClick={() => setShowCreateRole(true)}
            className="create-role-button"
          >
            Create Custom Role
          </button>
        )}
      </div>

      {/* Business Roles Section */}
      <div className="business-roles-section">
        <h4>📋 Business Roles</h4>
        <div className="roles-grid">
          {businessRoles.map(role => (
            <div key={role.id} className="role-card">
              <div className="role-header">
                <h5>{role.role_name}</h5>
              </div>
              <div className="role-permissions">
                <div className="permission-item">
                  <span>{getPermissionIcon(role.can_manage_finances)} Manage Finances</span>
                </div>
                <div className="permission-item">
                  <span>{getPermissionIcon(role.can_approve_payments)} Approve Payments</span>
                </div>
                <div className="permission-item">
                  <span>{getPermissionIcon(role.can_manage_alliances)} Manage Alliance</span>
                </div>
                <div className="permission-item">
                  <span>{getPermissionIcon(role.can_manage_users)} Manage Users</span>
                </div>
                <div className="permission-item">
                  <span>{getPermissionIcon(role.can_view_reports)} View Reports</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Member Role Assignments */}
      <div className="member-roles-section">
        <h4>👥 Member Role Assignments</h4>
        <div className="members-roles-list">
          {members.map(member => {
            const memberRole = getRolePermissions(member.businessRole);
            return (
              <div key={member.id} className="member-role-card">
                <div className="member-info">
                  <div className="member-name">{member.displayName}</div>
                  <div className="member-email">{member.email}</div>
                </div>
                <div className="member-role-assignment">
                  <div className="current-role">
                    <span className="role-badge">{memberRole?.role_name}</span>
                  </div>
                  {isFounder && member.role !== 'owner' && (
                    <div className="role-selector">
                      <select
                        value={member.businessRole}
                        onChange={(e) => handleAssignRole(member.id, e.target.value)}
                        disabled={loading}
                      >
                        {businessRoles.map(role => (
                          <option key={role.id} value={role.id}>
                            {role.role_name}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}
                </div>
                <div className="member-permissions-summary">
                  {memberRole?.can_manage_finances && <span className="permission-badge">💰</span>}
                  {memberRole?.can_approve_payments && <span className="permission-badge">✅</span>}
                  {memberRole?.can_manage_alliances && <span className="permission-badge">🏰</span>}
                  {memberRole?.can_manage_users && <span className="permission-badge">👥</span>}
                  {memberRole?.can_view_reports && <span className="permission-badge">📊</span>}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Create Custom Role Modal */}
      {showCreateRole && (
        <div className="create-role-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h4>Create Custom Role</h4>
              <button
                onClick={() => setShowCreateRole(false)}
                className="close-button"
              >
                ×
              </button>
            </div>
            
            <form onSubmit={handleCreateRole} className="create-role-form">
              <div className="form-group">
                <label htmlFor="role_name">Role Name</label>
                <input
                  id="role_name"
                  type="text"
                  value={newRole.role_name}
                  onChange={(e) => setNewRole(prev => ({ ...prev, role_name: e.target.value }))}
                  placeholder="e.g., 🎬 Creative Director"
                  required
                />
              </div>
              
              <div className="permissions-checkboxes">
                <h5>Permissions</h5>
                
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={newRole.can_manage_finances}
                    onChange={(e) => setNewRole(prev => ({ ...prev, can_manage_finances: e.target.checked }))}
                  />
                  <span>💰 Manage Finances</span>
                </label>
                
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={newRole.can_approve_payments}
                    onChange={(e) => setNewRole(prev => ({ ...prev, can_approve_payments: e.target.checked }))}
                  />
                  <span>✅ Approve Payments</span>
                </label>
                
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={newRole.can_manage_alliances}
                    onChange={(e) => setNewRole(prev => ({ ...prev, can_manage_alliances: e.target.checked }))}
                  />
                  <span>🏰 Manage Alliance</span>
                </label>
                
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={newRole.can_manage_users}
                    onChange={(e) => setNewRole(prev => ({ ...prev, can_manage_users: e.target.checked }))}
                  />
                  <span>👥 Manage Users</span>
                </label>
                
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={newRole.can_view_reports}
                    onChange={(e) => setNewRole(prev => ({ ...prev, can_view_reports: e.target.checked }))}
                  />
                  <span>📊 View Reports</span>
                </label>
              </div>
              
              <div className="form-actions">
                <button type="submit" disabled={loading} className="create-button">
                  {loading ? 'Creating...' : 'Create Role'}
                </button>
                <button
                  type="button"
                  onClick={() => setShowCreateRole(false)}
                  className="cancel-button"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default AlliancePermissions;
