import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, Button, Badge } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';

/**
 * CollaborationTools Section Component
 * Shows collaboration features and tools for teams
 * Part of the experimental navigation system
 */
const CollaborationTools = ({ canvasId, sectionId }) => {
  const { currentUser } = useContext(UserContext);
  const [activeTools, setActiveTools] = useState([]);

  const collaborationFeatures = [
    {
      id: 'chat',
      title: 'Team Chat',
      icon: '💬',
      description: 'Real-time messaging with your alliance members',
      status: 'coming-soon',
      color: 'primary'
    },
    {
      id: 'video-calls',
      title: 'Video Meetings',
      icon: '📹',
      description: 'Schedule and join video conferences',
      status: 'coming-soon',
      color: 'secondary'
    },
    {
      id: 'file-sharing',
      title: 'File Sharing',
      icon: '📁',
      description: 'Share documents and resources securely',
      status: 'coming-soon',
      color: 'success'
    },
    {
      id: 'project-boards',
      title: 'Project Boards',
      icon: '📋',
      description: 'Kanban-style project management',
      status: 'available',
      color: 'warning',
      action: () => window.location.href = '/projects'
    },
    {
      id: 'time-tracking',
      title: 'Time Tracking',
      icon: '⏱️',
      description: 'Track contributions and work hours',
      status: 'available',
      color: 'danger',
      action: () => window.location.href = '/track'
    },
    {
      id: 'revenue-sharing',
      title: 'Revenue Sharing',
      icon: '💰',
      description: 'Manage payments and royalty distribution',
      status: 'available',
      color: 'success',
      action: () => window.location.href = '/earn'
    }
  ];

  const integrationTools = [
    {
      id: 'github',
      title: 'GitHub',
      icon: '🐙',
      description: 'Connect your code repositories',
      status: 'coming-soon'
    },
    {
      id: 'slack',
      title: 'Slack',
      icon: '💬',
      description: 'Integrate with Slack workspaces',
      status: 'coming-soon'
    },
    {
      id: 'discord',
      title: 'Discord',
      icon: '🎮',
      description: 'Connect Discord servers',
      status: 'coming-soon'
    },
    {
      id: 'google-workspace',
      title: 'Google Workspace',
      icon: '📊',
      description: 'Sync with Google Drive and Docs',
      status: 'coming-soon'
    }
  ];

  const getStatusBadge = (status) => {
    switch (status) {
      case 'available':
        return <Badge color="success" variant="flat">Available</Badge>;
      case 'coming-soon':
        return <Badge color="warning" variant="flat">Coming Soon</Badge>;
      case 'beta':
        return <Badge color="primary" variant="flat">Beta</Badge>;
      default:
        return <Badge color="default" variant="flat">Unknown</Badge>;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-6 space-y-6"
    >
      {/* Section Header */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-white mb-2">🤝 Collaboration Tools</h2>
        <p className="text-white/70">
          Powerful tools to enhance team collaboration and productivity
        </p>
      </div>

      {/* Core Collaboration Features */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardBody className="p-6">
          <h3 className="text-xl font-bold text-white mb-6">🚀 Core Features</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {collaborationFeatures.map((feature) => (
              <motion.div
                key={feature.id}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`bg-white/5 rounded-lg p-4 cursor-pointer transition-all ${
                  feature.status === 'available' ? 'hover:bg-white/10' : 'opacity-75'
                }`}
                onClick={feature.action}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="text-3xl">{feature.icon}</div>
                  {getStatusBadge(feature.status)}
                </div>
                <h4 className="text-white font-medium mb-2">{feature.title}</h4>
                <p className="text-white/60 text-sm mb-3">{feature.description}</p>
                {feature.status === 'available' && (
                  <Button
                    size="sm"
                    color={feature.color}
                    variant="flat"
                    className="w-full"
                    onClick={feature.action}
                  >
                    Open Tool
                  </Button>
                )}
                {feature.status === 'coming-soon' && (
                  <Button
                    size="sm"
                    color="default"
                    variant="bordered"
                    className="w-full"
                    disabled
                  >
                    Coming Soon
                  </Button>
                )}
              </motion.div>
            ))}
          </div>
        </CardBody>
      </Card>

      {/* Integration Tools */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardBody className="p-6">
          <h3 className="text-xl font-bold text-white mb-6">🔗 Integrations</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
            {integrationTools.map((tool) => (
              <motion.div
                key={tool.id}
                whileHover={{ scale: 1.05 }}
                className="bg-white/5 rounded-lg p-4 text-center opacity-75"
              >
                <div className="text-4xl mb-3">{tool.icon}</div>
                <h4 className="text-white font-medium mb-2">{tool.title}</h4>
                <p className="text-white/60 text-xs mb-3">{tool.description}</p>
                {getStatusBadge(tool.status)}
              </motion.div>
            ))}
          </div>
        </CardBody>
      </Card>

      {/* Quick Actions */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardBody className="p-6">
          <h3 className="text-xl font-bold text-white mb-6">⚡ Quick Actions</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button
              color="primary"
              variant="solid"
              size="lg"
              startContent="📋"
              onClick={() => window.location.href = '/projects'}
              className="h-16"
            >
              <div className="text-center">
                <div className="font-bold">Projects</div>
                <div className="text-xs opacity-80">Manage work</div>
              </div>
            </Button>

            <Button
              color="secondary"
              variant="solid"
              size="lg"
              startContent="⏱️"
              onClick={() => window.location.href = '/track'}
              className="h-16"
            >
              <div className="text-center">
                <div className="font-bold">Track Time</div>
                <div className="text-xs opacity-80">Log contributions</div>
              </div>
            </Button>

            <Button
              color="success"
              variant="solid"
              size="lg"
              startContent="💰"
              onClick={() => window.location.href = '/earn'}
              className="h-16"
            >
              <div className="text-center">
                <div className="font-bold">Revenue</div>
                <div className="text-xs opacity-80">Share earnings</div>
              </div>
            </Button>

            <Button
              color="warning"
              variant="solid"
              size="lg"
              startContent="👥"
              onClick={() => window.location.href = '/teams'}
              className="h-16"
            >
              <div className="text-center">
                <div className="font-bold">Teams</div>
                <div className="text-xs opacity-80">Manage alliances</div>
              </div>
            </Button>
          </div>
        </CardBody>
      </Card>

      {/* Collaboration Tips */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardBody className="p-6">
          <h3 className="text-xl font-bold text-white mb-6">💡 Collaboration Tips</h3>
          <div className="space-y-4">
            <div className="flex items-start space-x-3 bg-white/5 rounded-lg p-4">
              <span className="text-2xl">🎯</span>
              <div>
                <h4 className="text-white font-medium">Set Clear Goals</h4>
                <p className="text-white/60 text-sm">Define project objectives and individual responsibilities upfront.</p>
              </div>
            </div>

            <div className="flex items-start space-x-3 bg-white/5 rounded-lg p-4">
              <span className="text-2xl">📅</span>
              <div>
                <h4 className="text-white font-medium">Regular Check-ins</h4>
                <p className="text-white/60 text-sm">Schedule weekly team meetings to track progress and address blockers.</p>
              </div>
            </div>

            <div className="flex items-start space-x-3 bg-white/5 rounded-lg p-4">
              <span className="text-2xl">📊</span>
              <div>
                <h4 className="text-white font-medium">Track Contributions</h4>
                <p className="text-white/60 text-sm">Use time tracking to ensure fair revenue distribution based on actual work.</p>
              </div>
            </div>

            <div className="flex items-start space-x-3 bg-white/5 rounded-lg p-4">
              <span className="text-2xl">🤝</span>
              <div>
                <h4 className="text-white font-medium">Open Communication</h4>
                <p className="text-white/60 text-sm">Encourage transparent communication and constructive feedback.</p>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default CollaborationTools;
