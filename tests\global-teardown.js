// Global teardown for Playwright tests
const fs = require('fs');
const path = require('path');

async function globalTeardown(config) {
  console.log('🧹 Cleaning up test environment...');
  
  try {
    // Clean up temporary files
    const authStatePath = path.join(__dirname, 'auth-state.json');
    if (fs.existsSync(authStatePath)) {
      fs.unlinkSync(authStatePath);
      console.log('🗑️ Cleaned up authentication state');
    }
    
    // Generate test summary
    const resultsPath = path.join(__dirname, '..', 'test-results', 'results.json');
    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      console.log('📊 Test Summary:');
      console.log(`   Total tests: ${results.stats?.total || 0}`);
      console.log(`   Passed: ${results.stats?.passed || 0}`);
      console.log(`   Failed: ${results.stats?.failed || 0}`);
      console.log(`   Skipped: ${results.stats?.skipped || 0}`);
    }
    
  } catch (error) {
    console.error('⚠️ Teardown warning:', error);
  }
  
  console.log('✅ Global teardown completed');
}

module.exports = globalTeardown;
