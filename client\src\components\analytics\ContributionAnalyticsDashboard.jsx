import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { format, subDays, startOfMonth, endOfMonth, parseISO, isValid } from 'date-fns';
import { toast } from 'react-hot-toast';
import { Card, CardBody, CardHeader, Button } from '../ui/heroui';

/**
 * ContributionAnalyticsDashboard Component
 *
 * Displays analytics and metrics for contributions
 * @param {Object} props - Component props
 * @param {string} props.projectId - Optional project ID to filter metrics by project
 * @param {string} props.userId - Optional user ID to filter metrics by user
 */
const ContributionAnalyticsDashboard = ({ projectId, userId }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dateRange, setDateRange] = useState('30days');
  const [contributorFilter, setContributorFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('default');
  const [contributors, setContributors] = useState([]);
  const [metrics, setMetrics] = useState({
    totalContributions: 0,
    totalHours: 0,
    averageHoursPerContribution: 0,
    averageDifficulty: 0,
    contributionsByStatus: {},
    contributionsByType: {},
    contributionsByCategory: {},
    contributionsByUser: {},
    contributionTrend: {},
    contributionsByDifficulty: {},
    validationStatusDistribution: {},
    contributionsByDay: {},
    contributionsByMonth: {},
    contributionEfficiency: {},
    contributionValueMetrics: {},
    topContributors: []
  });

  // Calculate date range based on selection
  const getDateRange = () => {
    const now = new Date();

    switch (dateRange) {
      case '7days':
        return { start: subDays(now, 7), end: now };
      case '30days':
        return { start: subDays(now, 30), end: now };
      case '90days':
        return { start: subDays(now, 90), end: now };
      case 'thisMonth':
        return { start: startOfMonth(now), end: endOfMonth(now) };
      default:
        return { start: subDays(now, 30), end: now };
    }
  };

  // Fetch contributors for the filter dropdown
  useEffect(() => {
    const fetchContributors = async () => {
      if (!currentUser) return;
      if (!projectId) return;

      try {
        // First, get the project contributors
        const { data: contributorsData, error: contributorsError } = await supabase
          .from('project_contributors')
          .select('user_id')
          .eq('project_id', projectId)
          .eq('status', 'active');

        if (contributorsError) throw contributorsError;

        // Then, get the user details separately
        const userIds = contributorsData.map(contributor => contributor.user_id);

        if (userIds.length > 0) {
          const { data: usersData, error: usersError } = await supabase
            .from('users')
            .select('id, display_name, email')
            .in('id', userIds);

          if (usersError) throw usersError;

          const formattedContributors = userIds.map(userId => {
            const user = usersData.find(u => u.id === userId);
            return {
              id: userId,
              name: user?.display_name || user?.email || 'Unknown'
            };
          });

          setContributors(formattedContributors);
        }
      } catch (err) {
        console.error('Error fetching contributors:', err);
      }
    };

    fetchContributors();
  }, [currentUser, projectId]);

  // Fetch contribution metrics
  useEffect(() => {
    const fetchContributionMetrics = async () => {
      if (!currentUser) return;

      setLoading(true);
      try {
        const { start, end } = getDateRange();

        // Format dates for Supabase query
        const startDate = format(start, 'yyyy-MM-dd');
        const endDate = format(end, 'yyyy-MM-dd');

        // Base query for contributions
        let query = supabase
          .from('contributions')
          .select(`
            id,
            project_id,
            user_id,
            task_name,
            task_type,
            category,
            difficulty,
            hours_spent,
            date_performed,
            created_at,
            validation_status
          `)
          .gte('created_at', startDate)
          .lte('created_at', endDate);

        // Add project filter if provided
        if (projectId) {
          query = query.eq('project_id', projectId);
        }

        // Add user filter if provided
        if (userId) {
          query = query.eq('user_id', userId);
        } else if (contributorFilter !== 'all') {
          query = query.eq('user_id', contributorFilter);
        }

        // Always filter by validation_status=approved by default
        // This ensures consistency with ProjectAnalyticsDashboard
        if (statusFilter === 'approved' || statusFilter === 'default') {
          query = query.eq('validation_status', 'approved');
        } else if (statusFilter !== 'all') {
          query = query.eq('validation_status', statusFilter);
        }

        // Execute query
        const { data: contributions, error: contributionsError } = await query;

        if (contributionsError) {
          throw contributionsError;
        }

        // Calculate metrics
        const totalContributions = contributions.length;
        const totalHours = contributions.reduce((sum, c) => sum + (parseFloat(c.hours_spent) || 0), 0);
        const averageHoursPerContribution = totalContributions > 0 ? totalHours / totalContributions : 0;
        const totalDifficulty = contributions.reduce((sum, c) => sum + (parseInt(c.difficulty) || 0), 0);
        const averageDifficulty = totalContributions > 0 ? totalDifficulty / totalContributions : 0;

        // Count contributions by status
        const contributionsByStatus = contributions.reduce((acc, c) => {
          acc[c.validation_status] = (acc[c.validation_status] || 0) + 1;
          return acc;
        }, {});

        // Count contributions by type
        const contributionsByType = contributions.reduce((acc, c) => {
          if (c.task_type) {
            acc[c.task_type] = (acc[c.task_type] || 0) + 1;
          }
          return acc;
        }, {});

        // Count contributions by category
        const contributionsByCategory = contributions.reduce((acc, c) => {
          if (c.category) {
            acc[c.category] = (acc[c.category] || 0) + 1;
          }
          return acc;
        }, {});

        // Count contributions by difficulty
        const contributionsByDifficulty = contributions.reduce((acc, c) => {
          const difficulty = c.difficulty ? c.difficulty.toString() : 'Unknown';
          acc[difficulty] = (acc[difficulty] || 0) + 1;
          return acc;
        }, {});

        // Get unique user IDs from contributions
        const userIds = [...new Set(contributions.map(c => c.user_id))];

        // Fetch user details
        let usersData = [];
        if (userIds.length > 0) {
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('id, display_name, email')
            .in('id', userIds);

          if (userError) {
            console.warn('Error fetching user details:', userError);
          } else {
            usersData = userData || [];
          }
        }

        // Create a map of user IDs to names
        const userNameMap = {};
        usersData.forEach(user => {
          userNameMap[user.id] = user.display_name || user.email || 'Unknown';
        });

        // Group contributions by user
        const contributionsByUser = {};
        for (const contribution of contributions) {
          const userId = contribution.user_id;
          const userName = userNameMap[userId] || 'Unknown';

          if (!contributionsByUser[userId]) {
            contributionsByUser[userId] = {
              userId,
              userName,
              count: 0,
              hours: 0,
              approved: 0,
              pending: 0,
              rejected: 0,
              pending_changes: 0
            };
          }

          contributionsByUser[userId].count++;
          contributionsByUser[userId].hours += parseFloat(contribution.hours_spent) || 0;

          // Count by validation status
          const status = contribution.validation_status || 'pending';
          contributionsByUser[userId][status] = (contributionsByUser[userId][status] || 0) + 1;
        }

        // Group contributions by date for trend analysis
        const contributionTrend = {};
        for (const contribution of contributions) {
          // Use date_performed if valid, otherwise fall back to created_at
          let dateStr = contribution.date_performed;
          if (!dateStr || !isValid(parseISO(dateStr))) {
            dateStr = contribution.created_at;
          }

          const date = format(new Date(dateStr), 'yyyy-MM-dd');

          if (!contributionTrend[date]) {
            contributionTrend[date] = {
              total: 0,
              hours: 0,
              approved: 0,
              pending: 0,
              rejected: 0,
              pending_changes: 0
            };
          }

          contributionTrend[date].total++;
          contributionTrend[date].hours += parseFloat(contribution.hours_spent) || 0;

          // Count by validation status
          const status = contribution.validation_status || 'pending';
          contributionTrend[date][status] = (contributionTrend[date][status] || 0) + 1;
        }

        // Calculate validation status distribution
        const validationStatusDistribution = {
          approved: contributionsByStatus.approved || 0,
          pending: contributionsByStatus.pending || 0,
          rejected: contributionsByStatus.rejected || 0,
          pending_changes: contributionsByStatus.pending_changes || 0
        };

        // Group contributions by day of week
        const contributionsByDay = contributions.reduce((acc, c) => {
          const date = parseISO(c.date_performed || c.created_at);
          if (isValid(date)) {
            const dayOfWeek = format(date, 'EEEE'); // Monday, Tuesday, etc.
            acc[dayOfWeek] = (acc[dayOfWeek] || 0) + 1;
          }
          return acc;
        }, {});

        // Group contributions by month
        const contributionsByMonth = contributions.reduce((acc, c) => {
          const date = parseISO(c.date_performed || c.created_at);
          if (isValid(date)) {
            const month = format(date, 'MMMM yyyy'); // January 2023, February 2023, etc.
            acc[month] = (acc[month] || 0) + 1;
          }
          return acc;
        }, {});

        // Calculate contribution efficiency (hours per difficulty level)
        const contributionEfficiency = contributions.reduce((acc, c) => {
          const difficulty = c.difficulty ? c.difficulty.toString() : 'Unknown';
          if (!acc[difficulty]) {
            acc[difficulty] = { hours: 0, count: 0 };
          }
          acc[difficulty].hours += parseFloat(c.hours_spent) || 0;
          acc[difficulty].count += 1;
          return acc;
        }, {});

        // Calculate contribution value metrics (weighted by difficulty)
        const contributionValueMetrics = {
          totalWeightedValue: 0,
          averageValuePerHour: 0,
          valueByType: {},
          valueByCategory: {}
        };

        contributions.forEach(c => {
          const difficulty = parseInt(c.difficulty) || 1;
          const hours = parseFloat(c.hours_spent) || 0;
          const value = hours * difficulty;

          contributionValueMetrics.totalWeightedValue += value;

          const type = c.task_type || 'Other';
          if (!contributionValueMetrics.valueByType[type]) {
            contributionValueMetrics.valueByType[type] = 0;
          }
          contributionValueMetrics.valueByType[type] += value;

          const category = c.category || 'Uncategorized';
          if (!contributionValueMetrics.valueByCategory[category]) {
            contributionValueMetrics.valueByCategory[category] = 0;
          }
          contributionValueMetrics.valueByCategory[category] += value;
        });

        if (totalHours > 0) {
          contributionValueMetrics.averageValuePerHour = contributionValueMetrics.totalWeightedValue / totalHours;
        }

        // Get top contributors
        const topContributors = Object.values(contributionsByUser)
          .sort((a, b) => b.count - a.count)
          .slice(0, 5);

        // Set metrics
        setMetrics({
          totalContributions,
          totalHours,
          averageHoursPerContribution,
          averageDifficulty,
          contributionsByStatus,
          contributionsByType,
          contributionsByCategory,
          contributionsByUser,
          contributionTrend,
          contributionsByDifficulty,
          validationStatusDistribution,
          contributionsByDay,
          contributionsByMonth,
          contributionEfficiency,
          contributionValueMetrics,
          topContributors
        });

      } catch (err) {
        console.error('Error fetching contribution metrics:', err);
        setError('Failed to load contribution metrics');
        toast.error('Failed to load contribution metrics');
      } finally {
        setLoading(false);
      }
    };

    fetchContributionMetrics();
  }, [currentUser, projectId, userId, dateRange, contributorFilter, statusFilter]);

  // Format time in hours, minutes
  const formatHours = (hours) => {
    if (isNaN(hours) || hours === 0) return '0 hrs';

    return `${hours.toFixed(1)} hrs`;
  };

  // Format percentage
  const formatPercentage = (value, total) => {
    if (total === 0) return '0%';
    return `${Math.round((value / total) * 100)}%`;
  };

  // Get trend data for chart
  const getTrendData = () => {
    const dates = Object.keys(metrics.contributionTrend).sort();
    return dates.map(date => ({
      date,
      ...metrics.contributionTrend[date]
    }));
  };

  // Handle date range change
  const handleDateRangeChange = (range) => {
    setDateRange(range);
  };

  // Handle contributor filter change
  const handleContributorFilterChange = (e) => {
    setContributorFilter(e.target.value);
  };

  // Export data as CSV
  const exportDataAsCSV = () => {
    // Get all contributions data
    const fetchAndExportData = async () => {
      try {
        const { start, end } = getDateRange();

        // Format dates for Supabase query
        const startDate = format(start, 'yyyy-MM-dd');
        const endDate = format(end, 'yyyy-MM-dd');

        // Base query for contributions
        let query = supabase
          .from('contributions')
          .select(`
            id,
            project_id,
            user_id,
            task_name,
            task_type,
            category,
            difficulty,
            hours_spent,
            date_performed,
            created_at,
            validation_status
          `)
          .gte('created_at', startDate)
          .lte('created_at', endDate);

        // Add project filter if provided
        if (projectId) {
          query = query.eq('project_id', projectId);
        }

        // Always filter by validation_status=approved by default
        // This ensures consistency with ProjectAnalyticsDashboard
        if (statusFilter === 'approved' || statusFilter === 'default') {
          query = query.eq('validation_status', 'approved');
        } else if (statusFilter !== 'all') {
          query = query.eq('validation_status', statusFilter);
        }

        // Add user filter if provided
        if (userId) {
          query = query.eq('user_id', userId);
        } else if (contributorFilter !== 'all') {
          query = query.eq('user_id', contributorFilter);
        }

        // Execute query
        const { data: contributions, error: contributionsError } = await query;

        if (contributionsError) {
          throw contributionsError;
        }

        // Get unique user IDs from contributions
        const userIds = [...new Set(contributions.map(c => c.user_id))];

        // Fetch user details
        let userNameMap = {};
        if (userIds.length > 0) {
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('id, display_name, email')
            .in('id', userIds);

          if (!userError && userData) {
            userData.forEach(user => {
              userNameMap[user.id] = user.display_name || user.email || 'Unknown';
            });
          }
        }

        // Format data for CSV
        const csvData = contributions.map(c => ({
          'Contribution ID': c.id,
          'Task Name': c.task_name,
          'Contributor': userNameMap[c.user_id] || 'Unknown',
          'Task Type': c.task_type || '',
          'Category': c.category || '',
          'Difficulty': c.difficulty || '',
          'Hours Spent': c.hours_spent || 0,
          'Date Performed': c.date_performed ? format(new Date(c.date_performed), 'yyyy-MM-dd') : '',
          'Created At': format(new Date(c.created_at), 'yyyy-MM-dd'),
          'Validation Status': c.validation_status || 'pending'
        }));

        // Convert to CSV
        const headers = Object.keys(csvData[0]);
        const csvContent = [
          headers.join(','),
          ...csvData.map(row => headers.map(header => {
            const value = row[header];
            // Wrap values with commas in quotes
            return typeof value === 'string' && value.includes(',')
              ? `"${value}"`
              : value;
          }).join(','))
        ].join('\n');

        // Create download link
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', `contributions-export-${format(new Date(), 'yyyy-MM-dd')}.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast.success('Data exported successfully');
      } catch (err) {
        console.error('Error exporting data:', err);
        toast.error('Failed to export data');
      }
    };

    fetchAndExportData();
  };

  if (loading) {
    return (
      <div className="contribution-analytics-dashboard loading">
        <div className="loading-spinner">
          <i className="bi bi-arrow-repeat spinning"></i>
          <span>Loading contribution metrics...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="contribution-analytics-dashboard error">
        <div className="error-message">
          <i className="bi bi-exclamation-triangle"></i>
          <span>{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="contribution-analytics-dashboard">
      <div className="dashboard-header">
        <h2>Contribution Analytics</h2>
        <div className="dashboard-controls">
          <div className="date-range-selector">
            <button
              className={`date-range-btn ${dateRange === '7days' ? 'active' : ''}`}
              onClick={() => handleDateRangeChange('7days')}
            >
              7 Days
            </button>
            <button
              className={`date-range-btn ${dateRange === '30days' ? 'active' : ''}`}
              onClick={() => handleDateRangeChange('30days')}
            >
              30 Days
            </button>
            <button
              className={`date-range-btn ${dateRange === '90days' ? 'active' : ''}`}
              onClick={() => handleDateRangeChange('90days')}
            >
              90 Days
            </button>
            <button
              className={`date-range-btn ${dateRange === 'thisMonth' ? 'active' : ''}`}
              onClick={() => handleDateRangeChange('thisMonth')}
            >
              This Month
            </button>
          </div>

          {projectId && contributors.length > 0 && (
            <div className="contributor-filter">
              <label htmlFor="contributor-select">Contributor:</label>
              <select
                id="contributor-select"
                value={contributorFilter}
                onChange={handleContributorFilterChange}
              >
                <option value="all">All Contributors</option>
                {contributors.map(contributor => (
                  <option key={contributor.id} value={contributor.id}>
                    {contributor.name}
                  </option>
                ))}
              </select>
            </div>
          )}

          <div className="status-filter">
            <label htmlFor="status-select">Status:</label>
            <select
              id="status-select"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="default">Approved Only (Default)</option>
              <option value="all">All Statuses</option>
              <option value="pending">Pending Only</option>
              <option value="rejected">Rejected Only</option>
              <option value="pending_changes">Changes Requested Only</option>
            </select>
          </div>

          <button
            className="export-btn"
            onClick={exportDataAsCSV}
            title="Export data as CSV"
          >
            <i className="bi bi-download"></i> Export
          </button>
        </div>
      </div>

      <div className="metrics-grid">
        {/* Total Contributions Metric */}
        <div className="metric-card">
          <div className="metric-icon">
            <i className="bi bi-list-check"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{metrics.totalContributions}</div>
            <div className="metric-label">Total Contributions</div>
          </div>
        </div>

        {/* Total Hours Metric */}
        <div className="metric-card">
          <div className="metric-icon">
            <i className="bi bi-clock-history"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{formatHours(metrics.totalHours)}</div>
            <div className="metric-label">Total Hours</div>
          </div>
        </div>

        {/* Average Hours Per Contribution Metric */}
        <div className="metric-card">
          <div className="metric-icon">
            <i className="bi bi-hourglass-split"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{formatHours(metrics.averageHoursPerContribution)}</div>
            <div className="metric-label">Avg. Hours Per Contribution</div>
          </div>
        </div>

        {/* Average Difficulty Metric */}
        <div className="metric-card">
          <div className="metric-icon">
            <i className="bi bi-bar-chart-steps"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{metrics.averageDifficulty.toFixed(1)}</div>
            <div className="metric-label">Avg. Difficulty</div>
          </div>
        </div>

        {/* Weighted Value Metric */}
        <div className="metric-card">
          <div className="metric-icon">
            <i className="bi bi-stars"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{metrics.contributionValueMetrics?.totalWeightedValue?.toFixed(1) || '0'}</div>
            <div className="metric-label">Total Weighted Value</div>
          </div>
        </div>
      </div>

      {/* Validation Status Distribution */}
      {metrics.totalContributions > 0 && (
        <div className="metrics-section">
          <h3 className="section-title">Validation Status Distribution</h3>
          <div className="status-distribution">
            <div className="distribution-chart">
              {Object.entries(metrics.validationStatusDistribution).map(([status, count]) => {
                const percentage = (count / metrics.totalContributions) * 100;
                const statusColors = {
                  approved: 'var(--success-color)',
                  rejected: 'var(--danger-color)',
                  pending_changes: 'var(--info-color)',
                  pending: 'var(--warning-color)'
                };

                const statusLabels = {
                  approved: 'Approved',
                  rejected: 'Rejected',
                  pending_changes: 'Changes Requested',
                  pending: 'Pending'
                };

                return (
                  <div key={status} className="distribution-bar-container">
                    <div className="distribution-label">
                      {statusLabels[status] || status}
                    </div>
                    <div className="distribution-bar">
                      <div
                        className="distribution-bar-fill"
                        style={{
                          width: `${percentage}%`,
                          backgroundColor: statusColors[status] || 'var(--secondary-color)'
                        }}
                      ></div>
                    </div>
                    <div className="distribution-value">
                      {count} ({Math.round(percentage)}%)
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Contribution Trend */}
      {Object.keys(metrics.contributionTrend).length > 0 && (
        <div className="metrics-section">
          <h3 className="section-title">Contribution Trend</h3>
          <div className="trend-chart">
            <div className="trend-chart-container">
              {getTrendData().map((day, index) => (
                <div key={day.date} className="trend-day">
                  <div className="trend-bars">
                    <div
                      className="trend-bar hours"
                      style={{
                        height: `${(day.hours / Math.max(...getTrendData().map(d => d.hours || 0))) * 100}%`
                      }}
                      title={`${day.hours.toFixed(1)} hours on ${day.date}`}
                    ></div>
                    <div
                      className="trend-bar count"
                      style={{
                        height: `${(day.total / Math.max(...getTrendData().map(d => d.total))) * 100}%`
                      }}
                      title={`${day.total} contributions on ${day.date}`}
                    ></div>
                  </div>
                  <div className="trend-date">
                    {format(new Date(day.date), 'MM/dd')}
                  </div>
                </div>
              ))}
            </div>
            <div className="trend-legend">
              <div className="legend-item">
                <div className="legend-color hours"></div>
                <div className="legend-label">Hours</div>
              </div>
              <div className="legend-item">
                <div className="legend-color count"></div>
                <div className="legend-label">Contributions</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Contributions by Type */}
      {Object.keys(metrics.contributionsByType).length > 0 && (
        <div className="metrics-section">
          <h3 className="section-title">Contributions by Type</h3>
          <div className="type-distribution">
            <div className="distribution-chart">
              {Object.entries(metrics.contributionsByType)
                .sort((a, b) => b[1] - a[1])
                .map(([type, count]) => {
                  const percentage = (count / metrics.totalContributions) * 100;
                  return (
                    <div key={type} className="distribution-bar-container">
                      <div className="distribution-label">
                        {type}
                      </div>
                      <div className="distribution-bar">
                        <div
                          className="distribution-bar-fill type-bar"
                          style={{
                            width: `${percentage}%`
                          }}
                        ></div>
                      </div>
                      <div className="distribution-value">
                        {count} ({Math.round(percentage)}%)
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        </div>
      )}

      {/* Contributions by Category */}
      {Object.keys(metrics.contributionsByCategory).length > 0 && (
        <div className="metrics-section">
          <h3 className="section-title">Contributions by Category</h3>
          <div className="category-distribution">
            <div className="distribution-chart">
              {Object.entries(metrics.contributionsByCategory)
                .sort((a, b) => b[1] - a[1])
                .map(([category, count]) => {
                  const percentage = (count / metrics.totalContributions) * 100;
                  return (
                    <div key={category} className="distribution-bar-container">
                      <div className="distribution-label">
                        {category}
                      </div>
                      <div className="distribution-bar">
                        <div
                          className="distribution-bar-fill category-bar"
                          style={{
                            width: `${percentage}%`
                          }}
                        ></div>
                      </div>
                      <div className="distribution-value">
                        {count} ({Math.round(percentage)}%)
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        </div>
      )}

      {/* Contributions by Difficulty */}
      {Object.keys(metrics.contributionsByDifficulty).length > 0 && (
        <div className="metrics-section">
          <h3 className="section-title">Contributions by Difficulty</h3>
          <div className="difficulty-distribution">
            <div className="distribution-chart">
              {Object.entries(metrics.contributionsByDifficulty)
                .sort((a, b) => parseInt(a[0]) - parseInt(b[0]))
                .map(([difficulty, count]) => {
                  const percentage = (count / metrics.totalContributions) * 100;
                  return (
                    <div key={difficulty} className="distribution-bar-container">
                      <div className="distribution-label">
                        {difficulty === 'Unknown' ? 'Unknown' : `Level ${difficulty}`}
                      </div>
                      <div className="distribution-bar">
                        <div
                          className="distribution-bar-fill difficulty-bar"
                          style={{
                            width: `${percentage}%`,
                            opacity: difficulty === 'Unknown' ? 0.5 : (parseInt(difficulty) / 8)
                          }}
                        ></div>
                      </div>
                      <div className="distribution-value">
                        {count} ({Math.round(percentage)}%)
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        </div>
      )}

      {/* Contributor Performance */}
      {Object.keys(metrics.contributionsByUser).length > 0 && (
        <div className="metrics-section">
          <h3 className="section-title">Contributor Performance</h3>
          <div className="contributor-table-container">
            <table className="contributor-table">
              <thead>
                <tr>
                  <th>Contributor</th>
                  <th>Contributions</th>
                  <th>Hours</th>
                  <th>Approved</th>
                  <th>Pending</th>
                  <th>Avg. Hours</th>
                </tr>
              </thead>
              <tbody>
                {Object.values(metrics.contributionsByUser)
                  .sort((a, b) => b.count - a.count)
                  .map(contributor => {
                    const avgHours = contributor.count > 0
                      ? contributor.hours / contributor.count
                      : 0;

                    return (
                      <tr key={contributor.userId}>
                        <td>{contributor.userName}</td>
                        <td>{contributor.count}</td>
                        <td>{formatHours(contributor.hours)}</td>
                        <td>
                          <div className="table-progress">
                            <div
                              className="table-progress-bar"
                              style={{ width: `${(contributor.approved / contributor.count) * 100}%` }}
                            ></div>
                            <span>{contributor.approved}</span>
                          </div>
                        </td>
                        <td>
                          <div className="table-progress pending">
                            <div
                              className="table-progress-bar"
                              style={{ width: `${(contributor.pending / contributor.count) * 100}%` }}
                            ></div>
                            <span>{contributor.pending}</span>
                          </div>
                        </td>
                        <td>{formatHours(avgHours)}</td>
                      </tr>
                    );
                  })
                }
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Contributions by Day of Week */}
      {Object.keys(metrics.contributionsByDay).length > 0 && (
        <div className="metrics-section">
          <h3 className="section-title">Contributions by Day of Week</h3>
          <div className="day-distribution">
            <div className="distribution-chart">
              {Object.entries(metrics.contributionsByDay)
                .sort((a, b) => {
                  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                  return days.indexOf(a[0]) - days.indexOf(b[0]);
                })
                .map(([day, count]) => {
                  const percentage = (count / metrics.totalContributions) * 100;
                  return (
                    <div key={day} className="distribution-bar-container">
                      <div className="distribution-label">
                        {day}
                      </div>
                      <div className="distribution-bar">
                        <div
                          className="distribution-bar-fill day-bar"
                          style={{
                            width: `${percentage}%`
                          }}
                        ></div>
                      </div>
                      <div className="distribution-value">
                        {count} ({Math.round(percentage)}%)
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        </div>
      )}

      {/* Contributions by Month */}
      {Object.keys(metrics.contributionsByMonth).length > 0 && (
        <div className="metrics-section">
          <h3 className="section-title">Contributions by Month</h3>
          <div className="month-distribution">
            <div className="distribution-chart">
              {Object.entries(metrics.contributionsByMonth)
                .sort((a, b) => {
                  const dateA = new Date(a[0]);
                  const dateB = new Date(b[0]);
                  return dateA - dateB;
                })
                .map(([month, count]) => {
                  const percentage = (count / metrics.totalContributions) * 100;
                  return (
                    <div key={month} className="distribution-bar-container">
                      <div className="distribution-label">
                        {month}
                      </div>
                      <div className="distribution-bar">
                        <div
                          className="distribution-bar-fill month-bar"
                          style={{
                            width: `${percentage}%`
                          }}
                        ></div>
                      </div>
                      <div className="distribution-value">
                        {count} ({Math.round(percentage)}%)
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        </div>
      )}

      {/* Contribution Efficiency */}
      {Object.keys(metrics.contributionEfficiency).length > 0 && (
        <div className="metrics-section">
          <h3 className="section-title">Contribution Efficiency</h3>
          <div className="efficiency-distribution">
            <div className="distribution-chart">
              {Object.entries(metrics.contributionEfficiency)
                .sort((a, b) => parseInt(a[0]) - parseInt(b[0]))
                .map(([difficulty, data]) => {
                  const avgHours = data.count > 0 ? data.hours / data.count : 0;
                  const maxAvgHours = Math.max(
                    ...Object.values(metrics.contributionEfficiency)
                      .map(d => d.count > 0 ? d.hours / d.count : 0)
                  );
                  const percentage = (avgHours / maxAvgHours) * 100;

                  return (
                    <div key={difficulty} className="distribution-bar-container">
                      <div className="distribution-label">
                        {difficulty === 'Unknown' ? 'Unknown' : `Level ${difficulty}`}
                      </div>
                      <div className="distribution-bar">
                        <div
                          className="distribution-bar-fill efficiency-bar"
                          style={{
                            width: `${percentage}%`,
                            opacity: difficulty === 'Unknown' ? 0.5 : (parseInt(difficulty) / 8)
                          }}
                        ></div>
                      </div>
                      <div className="distribution-value">
                        {formatHours(avgHours)} per contribution
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContributionAnalyticsDashboard;
