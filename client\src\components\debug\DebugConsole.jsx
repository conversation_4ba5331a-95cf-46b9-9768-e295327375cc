import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button, Input } from '@heroui/react';

/**
 * Debug Console Component
 *
 * Activated by Konami Code: ↑↑↓↓←→←→BA
 * Provides testing utilities and debug commands
 */
const DebugConsole = ({ isOpen, onClose, onCommand }) => {
  const [command, setCommand] = useState('');
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const commands = {
    'intro': 'Restart onboarding tutorial',
    'clear-storage': 'Clear all localStorage data',
    'grid': 'Switch to grid view',
    'overworld': 'Switch to overworld view',
    'content': 'Switch to content view',
    'reset-view': 'Reset navigation view position',
    'tutorial': 'Enable tutorial mode',
    'debug-ui': 'Toggle debug UI overlay',
    'crosshair': 'Toggle mouse crosshair lines',
    'help': 'Show available commands',
    'clear': 'Clear console history'
  };

  const executeCommand = (cmd) => {
    const trimmedCmd = cmd.trim().toLowerCase();
    const timestamp = new Date().toLocaleTimeString();

    // Add to history
    setHistory(prev => [...prev, { type: 'input', text: `> ${cmd}`, timestamp }]);

    let output = '';

    switch (trimmedCmd) {
      case 'intro':
        // Clear onboarding completion and reload
        localStorage.clear();
        output = 'Onboarding data cleared. Reloading page...';
        setTimeout(() => window.location.reload(), 1000);
        break;

      case 'clear-storage':
        localStorage.clear();
        output = 'All localStorage data cleared.';
        break;

      case 'grid':
        onCommand('setViewMode', 'grid');
        output = 'Switched to grid view.';
        break;

      case 'overworld':
        onCommand('setViewMode', 'overworld');
        output = 'Switched to overworld view.';
        break;

      case 'content':
        onCommand('setViewMode', 'content');
        output = 'Switched to content view.';
        break;

      case 'reset-view':
        onCommand('resetView');
        output = 'Navigation view position reset.';
        break;

      case 'tutorial':
        onCommand('enableTutorial');
        output = 'Tutorial mode enabled.';
        break;

      case 'debug-ui':
        onCommand('showDebugUI');
        output = 'Debug UI overlay toggled.';
        break;

      case 'crosshair':
        onCommand('toggleCrosshair');
        output = 'Mouse crosshair toggled.';
        break;

      case 'help':
        output = 'Available commands:\n' +
          Object.entries(commands).map(([cmd, desc]) => `  ${cmd} - ${desc}`).join('\n');
        break;

      case 'clear':
        setHistory([]);
        setCommand('');
        return;

      default:
        output = `Unknown command: ${trimmedCmd}. Type 'help' for available commands.`;
    }

    // Add output to history
    if (output) {
      setHistory(prev => [...prev, { type: 'output', text: output, timestamp }]);
    }

    setCommand('');
    setHistoryIndex(-1);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      executeCommand(command);
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      // Navigate command history up
      if (historyIndex < history.filter(h => h.type === 'input').length - 1) {
        const newIndex = historyIndex + 1;
        setHistoryIndex(newIndex);
        const inputHistory = history.filter(h => h.type === 'input');
        setCommand(inputHistory[inputHistory.length - 1 - newIndex]?.text.replace('> ', '') || '');
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      // Navigate command history down
      if (historyIndex > 0) {
        const newIndex = historyIndex - 1;
        setHistoryIndex(newIndex);
        const inputHistory = history.filter(h => h.type === 'input');
        setCommand(inputHistory[inputHistory.length - 1 - newIndex]?.text.replace('> ', '') || '');
      } else if (historyIndex === 0) {
        setHistoryIndex(-1);
        setCommand('');
      }
    } else if (e.key === 'Escape') {
      onClose();
    }
  };

  // Drag handlers
  const handleMouseDown = (e) => {
    if (e.target.closest('.debug-console-header')) {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y
      });
    }
  };

  const handleMouseMove = (e) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Add global mouse event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragStart]);

  // Auto-scroll to bottom when history updates
  useEffect(() => {
    const consoleBody = document.getElementById('debug-console-body');
    if (consoleBody) {
      consoleBody.scrollTop = consoleBody.scrollHeight;
    }
  }, [history]);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-[9999] bg-black bg-opacity-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        >
          <motion.div
            className="absolute w-full max-w-2xl max-h-[80vh] flex flex-col"
            style={{
              left: `calc(50% + ${position.x}px)`,
              top: `calc(50% + ${position.y}px)`,
              transform: 'translate(-50%, -50%)',
              cursor: isDragging ? 'grabbing' : 'default'
            }}
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            onClick={(e) => e.stopPropagation()}
            onMouseDown={handleMouseDown}
          >
            <Card className="bg-gray-900 border border-green-500 text-green-400 font-mono">
              <CardBody className="p-0">
                {/* Header */}
                <div className="debug-console-header flex items-center justify-between p-4 border-b border-green-500 cursor-grab active:cursor-grabbing select-none">
                  <h3 className="text-lg font-bold">🎮 Debug Console</h3>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      onClick={() => executeCommand('help')}
                      className="bg-green-600 text-white"
                    >
                      Help
                    </Button>
                    <Button
                      size="sm"
                      onClick={onClose}
                      className="bg-red-600 text-white"
                    >
                      ✕
                    </Button>
                  </div>
                </div>

                {/* Console Body */}
                <div
                  id="debug-console-body"
                  className="h-64 overflow-y-auto p-4 bg-black text-sm"
                >
                  {history.length === 0 && (
                    <div className="text-green-300 opacity-70">
                      Royaltea Debug Console v1.0<br/>
                      Type 'help' for available commands.<br/>
                      Press ESC to close.<br/>
                    </div>
                  )}
                  {history.map((entry, index) => (
                    <div
                      key={index}
                      className={`mb-1 ${entry.type === 'input' ? 'text-green-400' : 'text-green-300'}`}
                    >
                      <span className="text-gray-500 text-xs mr-2">{entry.timestamp}</span>
                      <span className="whitespace-pre-wrap">{entry.text}</span>
                    </div>
                  ))}
                </div>

                {/* Input */}
                <div className="p-4 border-t border-green-500">
                  <div className="flex items-center gap-2">
                    <span className="text-green-400">{'>'}</span>
                    <Input
                      value={command}
                      onChange={(e) => setCommand(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder="Enter command..."
                      className="flex-1 bg-transparent border-none text-green-400 font-mono"
                      classNames={{
                        input: "bg-transparent text-green-400 font-mono",
                        inputWrapper: "bg-transparent border-none shadow-none"
                      }}
                      autoFocus
                    />
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default DebugConsole;
